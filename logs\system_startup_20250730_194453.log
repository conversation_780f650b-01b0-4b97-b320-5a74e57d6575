2025-07-30 19:44:53,988 - __main__ - INFO - ============================================================
2025-07-30 19:44:53,988 - __main__ - INFO - 行星轮系参数化建模和自动装配系统
2025-07-30 19:44:53,988 - __main__ - INFO - ============================================================
2025-07-30 19:44:53,988 - __main__ - INFO - Python版本: 3.11.4 | packaged by Anaconda, Inc. | (main, Jul  5 2023, 13:38:37) [MSC v.1916 64 bit (AMD64)]
2025-07-30 19:44:53,988 - __main__ - INFO - 工作目录: C:\Users\<USER>\Desktop\kaifa\final
2025-07-30 19:44:53,988 - __main__ - INFO - 步骤 1/5: 检查依赖项
2025-07-30 19:44:53,989 - __main__ - INFO - 开始检查依赖项...
2025-07-30 19:44:54,013 - __main__ - INFO - ✓ win32com.client - 已安装
2025-07-30 19:44:54,013 - __main__ - INFO - ✓ tkinter - 已安装
2025-07-30 19:44:54,013 - __main__ - INFO - ✓ json - 已安装
2025-07-30 19:44:54,013 - __main__ - INFO - ✓ math - 已安装
2025-07-30 19:44:54,013 - __main__ - INFO - ✓ pathlib - 已安装
2025-07-30 19:44:54,013 - __main__ - INFO - ✓ datetime - 已安装
2025-07-30 19:44:54,236 - __main__ - INFO - ✓ pycatia - 已安装 (可选)
2025-07-30 19:44:54,236 - __main__ - INFO - 依赖项检查完成
2025-07-30 19:44:54,236 - __main__ - ERROR - 启动错误: name 'llogger' is not defined
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\kaifa\final\启动系统.py", line 269, in main
    llogger.info("步骤 2/5: 检查项目结构")
    ^^^^^^^
NameError: name 'llogger' is not defined
