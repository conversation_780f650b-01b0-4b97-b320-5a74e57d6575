Language="VBSCRIPT"

' ==========================================
' 此文件由VBA脚本管理器自动生成
' 生成时间: 2025-07-30 20:14:55
' 模板文件: revolve_planet.CATScript
' ==========================================


Sub CATMain()
    ' 行星轮公转脚本
    ' 参数将通过文件替换方式传入：3, 180.00, -1.000000, 0.000000
    
    ' 获取活动文档和产品
    Set productDocument1 = CATIA.ActiveDocument
    Set product1 = productDocument1.Product
    Set products1 = product1.Products
    
    ' 获取指定的行星轮
    Set planet = products1.Item("chilun.3")
    
    ' 绕Z轴公转指定角度
    Set move1 = planet.Move
    Set move1 = move1.MovableObject
    
    Dim arrayOfVariantOfDouble1(11)
    arrayOfVariantOfDouble1(0) = -1.000000
    arrayOfVariantOfDouble1(1) = -0.000000
    arrayOfVariantOfDouble1(2) = 0.000000
    arrayOfVariantOfDouble1(3) = 0.000000
    arrayOfVariantOfDouble1(4) = -1.000000
    arrayOfVariantOfDouble1(5) = 0.000000
    arrayOfVariantOfDouble1(6) = 0.000000
    arrayOfVariantOfDouble1(7) = 0.000000
    arrayOfVariantOfDouble1(8) = 1.000000
    arrayOfVariantOfDouble1(9) = 0.000000
    arrayOfVariantOfDouble1(10) = 0.000000
    arrayOfVariantOfDouble1(11) = 0.000000
    move1.Apply arrayOfVariantOfDouble1
    
    ' 更新产品
    product1.Update
    
End Sub
