Language="VBSCRIPT"

' ==========================================
' 此文件由VBA脚本管理器自动生成
' 生成时间: 2025-07-30 18:43:19
' 模板文件: assemble_ring_gear.CATScript
' ==========================================


Sub CATMain()
    ' 内齿圈装配脚本
    ' 参数将通过文件替换方式传入：C:/Users/<USER>/Desktop/kaifa/final/r.CATPart, chigen_p, 偶数齿啮合面
    ' 内齿圈啮合面固定为"啮合面r"

    ' 获取活动文档和产品
    Set productDocument1 = CATIA.ActiveDocument
    Set product1 = productDocument1.Product
    Set products1 = product1.Products

    ' 添加内齿圈零件
    Dim arrayOfVariantOfBSTR1(0)
    arrayOfVariantOfBSTR1(0) = "C:/Users/<USER>/Desktop/kaifa/final/r.CATPart"
    products1.AddComponentsFromFiles arrayOfVariantOfBSTR1, "All"

    ' 创建约束系统
    Set constraints1 = product1.Connections("CATIAConstraints")
    
    ' 内齿圈中心轴线与太阳轮中心轴线重合
    Set reference1 = product1.CreateReferenceFromName("Product1/ring.1/!Axis:(Selection_RSur:(Face:(Brp:(Pad.1;0:(Brp:(Sketch.6;1)));None:();Cf12:());CircPattern.1_ResultOUT;Z0;G8782))")
    Set reference2 = product1.CreateReferenceFromName("Product1/sun.1/!Axis:(Selection_RSur:(Face:(Brp:(Pocket.1;0:(Brp:(Sketch.10;1)));None:();Cf12:());Pocket.1_ResultOUT;Z0;G8782))")
    Set constraint1 = constraints1.AddBiEltCst(2, reference1, reference2)
    
    ' 内齿圈中心面与太阳轮中心面重合
    Set constraints1 = product1.Connections("CATIAConstraints")
    Set reference3 = product1.CreateReferenceFromName("Product1/ring.1/!渐开线内齿轮/中心面r")
    Set reference4 = product1.CreateReferenceFromName("Product1/sun.1/!零件几何体/center_plane_s")
    Set constraint2 = constraints1.AddBiEltCst(2, reference3, reference4)
    constraint2.Orientation = 0
    product1.Update
    
    ' 内齿圈啮合面与行星轮啮合面重合
    Set constraints1 = product1.Connections("CATIAConstraints")
    Set reference5 = product1.CreateReferenceFromName("Product1/ring.1/!渐开线内齿轮/啮合面r")
    Set reference6 = product1.CreateReferenceFromName("Product1/chilun.1/!零件几何体/chigen_p")
    Set constraint3 = constraints1.AddBiEltCst(2, reference5, reference6)
    constraint3.Orientation = 0
    product1.Update
    
End Sub
