2025-07-30 04:15:58,568 - __main__ - INFO - ============================================================
2025-07-30 04:15:58,568 - __main__ - INFO - 行星轮系参数化建模和自动装配系统
2025-07-30 04:15:58,568 - __main__ - INFO - ============================================================
2025-07-30 04:15:58,568 - __main__ - INFO - Python版本: 3.11.4 | packaged by Anaconda, Inc. | (main, Jul  5 2023, 13:38:37) [MSC v.1916 64 bit (AMD64)]
2025-07-30 04:15:58,569 - __main__ - INFO - 工作目录: C:\Users\<USER>\Desktop\kaifa\final
2025-07-30 04:15:58,569 - __main__ - INFO - 步骤 1/5: 检查依赖项
2025-07-30 04:15:58,569 - __main__ - INFO - 开始检查依赖项...
2025-07-30 04:15:58,616 - __main__ - INFO - ✓ win32com.client - 已安装
2025-07-30 04:15:58,616 - __main__ - INFO - ✓ tkinter - 已安装
2025-07-30 04:15:58,616 - __main__ - INFO - ✓ json - 已安装
2025-07-30 04:15:58,617 - __main__ - INFO - ✓ math - 已安装
2025-07-30 04:15:58,617 - __main__ - INFO - ✓ pathlib - 已安装
2025-07-30 04:15:58,617 - __main__ - INFO - ✓ datetime - 已安装
2025-07-30 04:15:58,857 - __main__ - INFO - ✓ pycatia - 已安装 (可选)
2025-07-30 04:15:58,857 - __main__ - INFO - 依赖项检查完成
2025-07-30 04:15:58,857 - __main__ - INFO - 步骤 2/5: 检查项目结构
2025-07-30 04:15:58,858 - __main__ - INFO - 开始检查项目结构...
2025-07-30 04:15:58,858 - __main__ - INFO - ✓ 文件存在: 行星轮系参数化建模系统.py
2025-07-30 04:15:58,858 - __main__ - INFO - ✓ 文件存在: vba_script_manager.py
2025-07-30 04:15:58,858 - __main__ - INFO - ✓ 文件存在: config_manager.py
2025-07-30 04:15:58,859 - __main__ - INFO - ✓ 目录存在: vba_scripts
2025-07-30 04:15:58,859 - __main__ - INFO - ✓ CATIA文件存在: sun.CATPart
2025-07-30 04:15:58,859 - __main__ - INFO - ✓ CATIA文件存在: p.CATPart
2025-07-30 04:15:58,859 - __main__ - INFO - ✓ CATIA文件存在: r.CATPart
2025-07-30 04:15:58,860 - __main__ - INFO - 项目结构检查完成
2025-07-30 04:15:58,860 - __main__ - INFO - 步骤 3/5: 检查VBA脚本
2025-07-30 04:15:58,860 - __main__ - INFO - 开始检查VBA脚本...
2025-07-30 04:15:58,860 - __main__ - INFO - ✓ VBA脚本存在: assemble_ring_gear.CATScript
2025-07-30 04:15:58,860 - __main__ - INFO - ✓ VBA脚本存在: mesh_first_planet.CATScript
2025-07-30 04:15:58,861 - __main__ - INFO - ✓ VBA脚本存在: move_planet.CATScript
2025-07-30 04:15:58,861 - __main__ - INFO - ✓ VBA脚本存在: revolve_planet.CATScript
2025-07-30 04:15:58,861 - __main__ - INFO - VBA脚本检查完成
2025-07-30 04:15:58,861 - __main__ - INFO - 步骤 4/5: 设置运行环境
2025-07-30 04:15:58,862 - __main__ - INFO - 开始设置运行环境...
2025-07-30 04:15:58,862 - config_manager - INFO - ✓ 目录已创建/存在: vba_scripts
2025-07-30 04:15:58,862 - config_manager - INFO - ✓ 目录已创建/存在: logs
2025-07-30 04:15:58,862 - config_manager - INFO - ✓ 目录已创建/存在: temp
2025-07-30 04:15:58,863 - config_manager - INFO - ✓ 目录已创建/存在: exports
2025-07-30 04:15:58,863 - __main__ - INFO - 运行环境设置完成
2025-07-30 04:15:58,863 - __main__ - INFO - 步骤 5/5: 启动主程序
2025-07-30 04:15:58,878 - __main__ - INFO - ✓ 主程序模块导入成功
2025-07-30 04:15:59,103 - __main__ - INFO - ✓ GUI界面创建成功
2025-07-30 04:15:59,103 - __main__ - INFO - ✓ 系统启动成功
2025-07-30 04:15:59,103 - __main__ - INFO - ✓ GUI界面已打开
2025-07-30 04:16:09,100 - __main__ - INFO - 用户关闭程序
2025-07-30 04:16:09,102 - config_manager - INFO - 配置文件保存成功: config.json
2025-07-30 04:16:09,103 - __main__ - INFO - 配置已保存
2025-07-30 04:16:09,150 - __main__ - INFO - 程序正常退出
