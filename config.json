{"catia_files": {"sun_gear": "sun.CATPart", "planet_gear": "p<PERSON><PERSON>", "ring_gear": "<PERSON><PERSON>"}, "default_parameters": {"sun_teeth": 20, "planet_teeth": 30, "ring_teeth": 80, "module": 2.0, "planet_count": 3, "pressure_angle": 20.0, "helix_angle": 0.0}, "ui_settings": {"window_width": 1200, "window_height": 800, "theme": "default", "font_size": 10, "language": "zh_CN"}, "logging": {"level": "INFO", "max_log_files": 10, "log_to_file": true, "log_to_console": true}, "catia_settings": {"auto_connect": true, "timeout": 30, "use_pycatia": true, "backup_before_operation": true}, "paths": {"vba_scripts": "vba_scripts", "logs": "logs", "temp": "temp", "exports": "exports"}}