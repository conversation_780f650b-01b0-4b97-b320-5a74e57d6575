Language="VBSCRIPT"

' ==========================================
' 此文件由VBA脚本管理器自动生成
' 生成时间: 2025-07-30 21:11:28
' 模板文件: rotate_ring.CATScript
' ==========================================


' 内齿圈自转脚本，思路与 revolve_planet 一致
' 由 Python 注入 2.368421、0.999146、0.041325、-0.041325

Sub CATMain()
    ' 角度参数
    Dim deltaDeg: deltaDeg = 2.368421    ' 替换为实际角度

    ' 获取活动文档
    Dim doc: Set doc = CATIA.ActiveDocument
    Dim prd: Set prd = doc.Product
    Dim ring: Set ring = prd.Products.Item("ring.1")  ' 默认内齿圈实例名 r.1

    ' 旋转
    Dim mv: Set mv = ring.Move
    Dim mo: Set mo = mv.MovableObject

    Dim m(11)
    m(0) = 0.999146
    m(1) = -0.041325
    m(2) = 0
    m(3) = 0.041325
    m(4) = 0.999146
    m(5) = 0
    m(6) = 0
    m(7) = 0
    m(8) = 1
    m(9) = 0
    m(10)= 0
    m(11)= 0

    mo.Apply m
    prd.Update
End Sub