Language="VBSCRIPT"

' ==========================================
' 此文件由VBA脚本管理器自动生成
' 生成时间: 2025-07-30 20:04:00
' 模板文件: mesh_first_planet.CATScript
' ==========================================


Sub CATMain()
    ' 第一个行星轮啮合约束脚本
    ' 参数将通过文件替换方式传入：435.000000, chiding_p, 奇数齿啮合面

    ' 获取活动文档和产品
    Set productDocument1 = CATIA.ActiveDocument
    Set product1 = productDocument1.Product
    Set products1 = product1.Products

    ' 步骤1: 固定太阳轮
    Set sun1 = products1.Item("sun.1")
    Set constraints1 = product1.Connections("CATIAConstraints")
    Set reference_sun = product1.CreateReferenceFromName("Product1/sun.1/!Product1/sun.1/")
    Set constraint_fix = constraints1.AddMonoEltCst(0, reference_sun)  ' 0 = 固定约束
    product1.Update

    ' 步骤2: 获取第一个行星轮
    Set planet1 = products1.Item("chilun.1")

    ' 步骤3: 先移动第一个行星轮到中心距位置
    Set move1 = planet1.Move
    Set move1 = move1.MovableObject

    Dim arrayOfVariantOfDouble1(11)
    arrayOfVariantOfDouble1(0) = 1.000000
    arrayOfVariantOfDouble1(1) = 0.000000
    arrayOfVariantOfDouble1(2) = 0.000000
    arrayOfVariantOfDouble1(3) = 0.000000
    arrayOfVariantOfDouble1(4) = 1.000000
    arrayOfVariantOfDouble1(5) = 0.000000
    arrayOfVariantOfDouble1(6) = 0.000000
    arrayOfVariantOfDouble1(7) = 0.000000
    arrayOfVariantOfDouble1(8) = 1.000000
    arrayOfVariantOfDouble1(9) = 435.000000
    arrayOfVariantOfDouble1(10) = 0.000000
    arrayOfVariantOfDouble1(11) = 0.000000
    move1.Apply arrayOfVariantOfDouble1

    ' 步骤4: 中心面相合约束
    Set constraints1 = product1.Connections("CATIAConstraints")
    Set reference1 = product1.CreateReferenceFromName("Product1/sun.1/!零件几何体/center_plane_s")
    Set reference2 = product1.CreateReferenceFromName("Product1/chilun.1/!零件几何体/center_plane_p")
    Set constraint1 = constraints1.AddBiEltCst(2, reference1, reference2)  ' 2 = 相合约束
    constraint1.Orientation = 0
    product1.Update

    ' 步骤5: 中心距约束
    Set product1 = product1.ReferenceProduct
    Set constraints1 = product1.Connections("CATIAConstraints")
    Set reference3 = product1.CreateReferenceFromName("Product1/sun.1/!Axis:(Selection_RSur:(Face:(Brp:(Pocket.1;0:(Brp:(Sketch.10;1)));None:();Cf12:());Pocket.1_ResultOUT;Z0;G8782))")
    Set reference4 = product1.CreateReferenceFromName("Product1/chilun.1/!Axis:(Selection_RSur:(Face:(Brp:(Pocket.1;0:(Brp:(Sketch.10;1)));None:();Cf12:());Pocket.1_ResultOUT;Z0;G8782))")
    Set constraint2 = constraints1.AddBiEltCst(catCstTypeDistance, reference3, reference4)  ' 3 = 距离约束
    Set length1 = constraint2.Dimension
    length1.Value = 435.000000
    constraint2.DistanceConfig = 0
    product1.Update

    ' 步骤6: 啮合面相合约束
    Set constraints1 = product1.Connections("CATIAConstraints")
    Set reference5 = product1.CreateReferenceFromName("Product1/sun.1/!零件几何体/chiding_s")
    Set reference6 = product1.CreateReferenceFromName("Product1/chilun.1/!零件几何体/chiding_p")
    Set constraint3 = constraints1.AddBiEltCst(2, reference5, reference6)  ' 2 = 相合约束
    constraint3.Orientation = 0
    product1.Update
    
End Sub
