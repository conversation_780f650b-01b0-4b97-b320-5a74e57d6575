Language="VBSCRIPT"

' 行星轮系自动装配脚本
' 生成时间: 2025-07-30 21:11:17
' 参数配置:
'   太阳轮齿数: 20
'   行星轮齿数: 28
'   内齿圈齿数: 76
'   行星轮数量: 3
'   中心距: 100.80 mm

Sub CATMain()

On Error Resume Next

Set productDocument1 = CATIA.ActiveDocument
Set product1 = productDocument1.Product
Set products1 = product1.Products

' 添加太阳轮
Dim arrayOfVariantOfBSTR1(0)
arrayOfVariantOfBSTR1(0) = "C:/Users/<USER>/Desktop/kaifa/final/sun.CATPart"
Call products1.AddComponentsFromFiles(arrayOfVariantOfBSTR1, "All")

Set constraints1 = product1.Connections("CATIAConstraints")
Set reference1 = product1.CreateReferenceFromName("Product1/sun.1/!Product1/sun.1/")
Set constraint1 = constraints1.AddMonoEltCst(catCstTypeReference, reference1)

' 添加行星轮
Dim arrayOfVariantOfBSTR2(0)
arrayOfVariantOfBSTR2(0) = "C:/Users/<USER>/Desktop/kaifa/final/p.CATPart"
Call products1.AddComponentsFromFiles(arrayOfVariantOfBSTR2, "All")


' 复制行星轮 2
Set selection2 = productDocument1.Selection
selection2.Clear
Set product2 = products1.Item("chilun.1")
selection2.Add product2
selection2.Copy
selection2.Clear
selection2.Add product1
selection2.Paste

' 复制行星轮 3
Set selection3 = productDocument1.Selection
selection3.Clear
Set product3 = products1.Item("chilun.1")
selection3.Add product3
selection3.Copy
selection3.Clear
selection3.Add product1
selection3.Paste

' 设置第一个行星轮装配约束
Set move1 = products1.Item("chilun.1").Move
Set move1 = move1.MovableObject

Dim arrayOfVariantOfDouble1(11)
arrayOfVariantOfDouble1(0) = 1.000000
arrayOfVariantOfDouble1(1) = 0.000000
arrayOfVariantOfDouble1(2) = 0.000000
arrayOfVariantOfDouble1(3) = 0.000000
arrayOfVariantOfDouble1(4) = 1.000000
arrayOfVariantOfDouble1(5) = 0.000000
arrayOfVariantOfDouble1(6) = 0.000000
arrayOfVariantOfDouble1(7) = 0.000000
arrayOfVariantOfDouble1(8) = 1.000000
arrayOfVariantOfDouble1(9) = 100.800000
arrayOfVariantOfDouble1(10) = 0.000000
arrayOfVariantOfDouble1(11) = 0.000000
Call move1.Apply(arrayOfVariantOfDouble1)

' 中心面相合约束
Set reference2 = product1.CreateReferenceFromName("Product1/sun.1/!零件几何体/center_plane_s")
Set reference3 = product1.CreateReferenceFromName("Product1/chilun.1/!零件几何体/center_plane_p")
Set constraint2 = constraints1.AddBiEltCst(catCstTypeOn, reference2, reference3)
constraint2.Orientation = catCstOrientSame
product1.Update

' 中心距约束
Set reference4 = product1.CreateReferenceFromName("Product1/sun.1/!圆形阵列.3/Z 轴")
Set reference5 = product1.CreateReferenceFromName("Product1/chilun.1/!圆形阵列.3/Z 轴")
Set constraint3 = constraints1.AddBiEltCst(catCstTypeDistance, reference4, reference5)
Set length1 = constraint3.Dimension
length1.Value = 100.800000
constraint3.DistanceConfig = catCstDCParallel
product1.Update

' 啮合面约束
Set reference6 = product1.CreateReferenceFromName("Product1/sun.1/!零件几何体/chiding_s")
Set reference7 = product1.CreateReferenceFromName("Product1/chilun.1/!零件几何体/chiding_p")
Set constraint4 = constraints1.AddBiEltCst(catCstTypeOn, reference6, reference7)
constraint4.Orientation = catCstOrientSame
product1.Update


' 注意：行星轮2及以后的精确位置将由Python pycatia程序设置
' 这样可以获得更高的精度和更好的控制

' 添加内齿圈
Dim arrayOfVariantOfBSTR3(0)
arrayOfVariantOfBSTR3(0) = "C:/Users/<USER>/Desktop/kaifa/final/r.CATPart"
Call products1.AddComponentsFromFiles(arrayOfVariantOfBSTR3, "All")

' 内齿圈装配约束
Set reference8 = product1.CreateReferenceFromName("Product1/ring.1/!Axis:(Selection_RSur:(Face:(Brp:(Pad.1;0:(Brp:(Sketch.6;1)));None:();Cf12:());CircPattern.1_ResultOUT;Z0;G8782))")
Set reference9 = product1.CreateReferenceFromName("Product1/sun.1/!Axis:(Selection_RSur:(Face:(Brp:(Pocket.1;0:(Brp:(Sketch.10;1)));None:();Cf12:());Pocket.1_ResultOUT;Z0;G8782))")
Set constraint5 = constraints1.AddBiEltCst(catCstTypeOn, reference8, reference9)

Set reference10 = product1.CreateReferenceFromName("Product1/ring.1/!渐开线内齿轮/中心面r")
Set reference11 = product1.CreateReferenceFromName("Product1/chilun.1/!零件几何体/center_plane_p")
Set constraint6 = constraints1.AddBiEltCst(catCstTypeOn, reference10, reference11)
constraint6.Orientation = catCstOrientSame
product1.Update

Set reference12 = product1.CreateReferenceFromName("Product1/ring.1/!渐开线内齿轮/啮合面r")
Set reference13 = product1.CreateReferenceFromName("Product1/chilun.1/!零件几何体/chiding_p")
Set constraint7 = constraints1.AddBiEltCst(catCstTypeOn, reference12, reference13)
constraint7.Orientation = catCstOrientSame
product1.Update

End Sub
