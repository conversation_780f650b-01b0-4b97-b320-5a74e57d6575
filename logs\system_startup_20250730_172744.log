2025-07-30 17:27:44,833 - __main__ - INFO - ============================================================
2025-07-30 17:27:44,834 - __main__ - INFO - 行星轮系参数化建模和自动装配系统
2025-07-30 17:27:44,834 - __main__ - INFO - ============================================================
2025-07-30 17:27:44,834 - __main__ - INFO - Python版本: 3.11.4 | packaged by Anaconda, Inc. | (main, Jul  5 2023, 13:38:37) [MSC v.1916 64 bit (AMD64)]
2025-07-30 17:27:44,834 - __main__ - INFO - 工作目录: C:\Users\<USER>\Desktop\kaifa\final
2025-07-30 17:27:44,834 - __main__ - INFO - 步骤 1/5: 检查依赖项
2025-07-30 17:27:44,834 - __main__ - INFO - 开始检查依赖项...
2025-07-30 17:27:44,860 - __main__ - INFO - ✓ win32com.client - 已安装
2025-07-30 17:27:44,861 - __main__ - INFO - ✓ tkinter - 已安装
2025-07-30 17:27:44,861 - __main__ - INFO - ✓ json - 已安装
2025-07-30 17:27:44,861 - __main__ - INFO - ✓ math - 已安装
2025-07-30 17:27:44,861 - __main__ - INFO - ✓ pathlib - 已安装
2025-07-30 17:27:44,861 - __main__ - INFO - ✓ datetime - 已安装
2025-07-30 17:27:45,093 - __main__ - INFO - ✓ pycatia - 已安装 (可选)
2025-07-30 17:27:45,094 - __main__ - INFO - 依赖项检查完成
2025-07-30 17:27:45,094 - __main__ - INFO - 步骤 2/5: 检查项目结构
2025-07-30 17:27:45,095 - __main__ - INFO - 开始检查项目结构...
2025-07-30 17:27:45,095 - __main__ - ERROR - ✗ 缺少文件: 行星轮系参数化建模系统.py
2025-07-30 17:27:45,095 - __main__ - INFO - ✓ 文件存在: vba_script_manager.py
2025-07-30 17:27:45,095 - __main__ - INFO - ✓ 文件存在: config_manager.py
2025-07-30 17:27:45,095 - __main__ - INFO - ✓ 目录存在: vba_scripts
2025-07-30 17:27:45,096 - __main__ - INFO - ✓ CATIA文件存在: sun.CATPart
2025-07-30 17:27:45,096 - __main__ - INFO - ✓ CATIA文件存在: p.CATPart
2025-07-30 17:27:45,096 - __main__ - INFO - ✓ CATIA文件存在: r.CATPart
2025-07-30 17:27:45,096 - __main__ - ERROR - 项目结构检查失败: ['行星轮系参数化建模系统.py']
2025-07-30 17:27:49,173 - __main__ - ERROR - 项目结构检查失败，程序退出
