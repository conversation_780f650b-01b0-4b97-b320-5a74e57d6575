#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
负责管理系统配置文件和参数
"""

import json
import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = Path(config_file)
        self.config = {}
        self.logger = logging.getLogger(__name__)
        
        # 默认配置
        self.default_config = {
            "catia_files": {
                "sun_gear": "sun.CATPart",
                "planet_gear": "p.CATPart", 
                "ring_gear": "r.CATPart"
            },
            "default_parameters": {
                "sun_teeth": 20,
                "planet_teeth": 30,
                "ring_teeth": 80,
                "module": 2.0,
                "planet_count": 3,
                "pressure_angle": 20.0,
                "helix_angle": 0.0
            },
            "ui_settings": {
                "window_width": 1200,
                "window_height": 800,
                "theme": "default",
                "font_size": 10,
                "language": "zh_CN"
            },
            "logging": {
                "level": "INFO",
                "max_log_files": 10,
                "log_to_file": True,
                "log_to_console": True
            },
            "catia_settings": {
                "auto_connect": True,
                "timeout": 30,
                "use_pycatia": True,
                "backup_before_operation": True
            },
            "paths": {
                "vba_scripts": "vba_scripts",
                "logs": "logs",
                "temp": "temp",
                "exports": "exports"
            }
        }
        
        self.load_config()
    
    def load_config(self) -> bool:
        """
        加载配置文件
        
        Returns:
            bool: 加载是否成功
        """
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
                self.logger.info(f"配置文件加载成功: {self.config_file}")
                
                # 合并默认配置（处理新增的配置项）
                self._merge_default_config()
                return True
            else:
                self.logger.info("配置文件不存在，使用默认配置")
                self.config = self.default_config.copy()
                self.save_config()
                return True
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            self.config = self.default_config.copy()
            return False
    
    def save_config(self) -> bool:
        """
        保存配置文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            # 确保配置文件目录存在
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4, ensure_ascii=False)
            
            self.logger.info(f"配置文件保存成功: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值（支持点分隔的嵌套键）
        
        Args:
            key: 配置键，支持 "section.key" 格式
            default: 默认值
            
        Returns:
            配置值
        """
        try:
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                value = value[k]
            
            return value
            
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """
        设置配置值（支持点分隔的嵌套键）
        
        Args:
            key: 配置键，支持 "section.key" 格式
            value: 配置值
            
        Returns:
            bool: 设置是否成功
        """
        try:
            keys = key.split('.')
            config = self.config
            
            # 导航到最后一级
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置值
            config[keys[-1]] = value
            return True
            
        except Exception as e:
            self.logger.error(f"设置配置值失败: {key}={value}, {e}")
            return False
    
    def _merge_default_config(self):
        """合并默认配置，添加缺失的配置项"""
        def merge_dict(default: dict, current: dict) -> dict:
            """递归合并字典"""
            for key, value in default.items():
                if key not in current:
                    current[key] = value
                elif isinstance(value, dict) and isinstance(current[key], dict):
                    merge_dict(value, current[key])
            return current
        
        self.config = merge_dict(self.default_config, self.config)
    
    def get_catia_file_path(self, gear_type: str) -> Optional[str]:
        """
        获取CATIA文件路径
        
        Args:
            gear_type: 齿轮类型 ('sun_gear', 'planet_gear', 'ring_gear')
            
        Returns:
            文件路径或None
        """
        return self.get(f"catia_files.{gear_type}")
    
    def get_default_parameter(self, param_name: str) -> Any:
        """
        获取默认参数值
        
        Args:
            param_name: 参数名称
            
        Returns:
            参数值
        """
        return self.get(f"default_parameters.{param_name}")
    
    def get_ui_setting(self, setting_name: str) -> Any:
        """
        获取UI设置
        
        Args:
            setting_name: 设置名称
            
        Returns:
            设置值
        """
        return self.get(f"ui_settings.{setting_name}")
    
    def create_directories(self):
        """创建必要的目录"""
        paths = self.get("paths", {})
        
        for path_name, path_value in paths.items():
            try:
                Path(path_value).mkdir(parents=True, exist_ok=True)
                self.logger.info(f"✓ 目录已创建/存在: {path_value}")
            except Exception as e:
                self.logger.error(f"创建目录失败 {path_value}: {e}")
    
    def validate_config(self) -> list:
        """
        验证配置文件
        
        Returns:
            list: 验证错误列表
        """
        errors = []
        
        # 检查必需的配置节
        required_sections = ['catia_files', 'default_parameters', 'ui_settings']
        for section in required_sections:
            if section not in self.config:
                errors.append(f"缺少配置节: {section}")
        
        # 检查CATIA文件路径
        catia_files = self.get("catia_files", {})
        for gear_type, file_path in catia_files.items():
            if file_path and not os.path.exists(file_path):
                errors.append(f"CATIA文件不存在: {file_path} ({gear_type})")
        
        # 检查数值参数
        numeric_params = ['sun_teeth', 'planet_teeth', 'ring_teeth', 'module', 'planet_count']
        for param in numeric_params:
            value = self.get_default_parameter(param)
            if not isinstance(value, (int, float)) or value <= 0:
                errors.append(f"参数值无效: {param}={value}")
        
        return errors

# 全局配置管理器实例
config_manager = ConfigManager()
