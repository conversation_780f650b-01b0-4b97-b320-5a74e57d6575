#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行星轮系参数化建模系统启动器 - PyQt版本
兼容Python 3.8.10环境
"""

import sys
import os
import subprocess
import importlib.util
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major != 3:
        print("错误: 需要Python 3.x版本")
        return False
    
    if version.minor < 8:
        print("警告: 建议使用Python 3.8或更高版本")
    
    return True

def check_required_packages():
    """检查必需的包"""
    # 必需的包
    required_packages = {
        'PyQt5': 'PyQt5',
        'win32com.client': 'pywin32'
    }

    # 可选的包
    optional_packages = {
        'pycatia': 'pycatia'
    }

    missing_packages = []

    # 检查必需包
    for package, install_name in required_packages.items():
        try:
            if package == 'win32com.client':
                import win32com.client
            elif package == 'PyQt5':
                import PyQt5.QtWidgets
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"✗ {package} 未安装")
            missing_packages.append(install_name)

    # 检查可选包
    for package, install_name in optional_packages.items():
        try:
            if package == 'pycatia':
                import pycatia
            print(f"✓ {package} 已安装")
        except ImportError:
            print(f"⚠ {package} 未安装 (可选，将使用win32com替代)")
    
    if missing_packages:
        print("\n缺少以下必需包，请安装:")
        for package in missing_packages:
            print(f"  pip install {package}")
        return False

    print("✓ 所有必需包已安装")
    return True

def check_project_structure():
    """检查项目结构"""
    required_files = [
        'planetary_gear_gui_pyqt.py',
        'catia_pycatia_position_manager.py',
        'vba_script_manager.py',
        'vba_scripts/mesh_first_planet.CATScript',
        'vba_scripts/move_planet.CATScript',
        'vba_scripts/revolve_planet.CATScript',
        'vba_scripts/assemble_ring_gear.CATScript',
        'vba_scripts/fix_sun_gear.CATScript'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
            print(f"✗ 缺少文件: {file_path}")
        else:
            print(f"✓ 文件存在: {file_path}")
    
    if missing_files:
        print(f"\n缺少 {len(missing_files)} 个必需文件")
        return False
    
    return True

def check_vba_scripts():
    """检查VBA脚本完整性"""
    vba_dir = Path("vba_scripts")
    if not vba_dir.exists():
        print("✗ VBA脚本目录不存在")
        return False
    
    script_files = list(vba_dir.glob("*.CATScript"))
    print(f"✓ 找到 {len(script_files)} 个VBA脚本文件")
    
    # 检查关键脚本
    key_scripts = [
        'mesh_first_planet.CATScript',
        'move_planet.CATScript', 
        'revolve_planet.CATScript',
        'assemble_ring_gear.CATScript',
        'fix_sun_gear.CATScript'
    ]
    
    missing_scripts = []
    for script in key_scripts:
        script_path = vba_dir / script
        if not script_path.exists():
            missing_scripts.append(script)
        else:
            # 检查脚本内容是否为空
            try:
                with open(script_path, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if not content:
                        print(f"⚠ 警告: {script} 文件为空")
                    else:
                        print(f"✓ {script} 检查通过")
            except Exception as e:
                print(f"⚠ 警告: 无法读取 {script}: {e}")
    
    if missing_scripts:
        print(f"✗ 缺少关键VBA脚本: {missing_scripts}")
        return False
    
    return True

def setup_environment():
    """设置环境"""
    # 创建必要的目录
    directories = [
        'generated_vba_scripts',
        'generated_vba_scripts/mesh_scripts',
        'generated_vba_scripts/revolve_scripts',
        'generated_vba_scripts/move_scripts',
        'generated_vba_scripts/assemble_scripts',
        'generated_vba_scripts/rotate_scripts',
        'generated_vba_scripts/delete_scripts',
        'generated_vba_scripts/fix_scripts',
        'generated_vba_scripts/archived_scripts',
        'logs',
        'configs'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ 目录已创建: {directory}")
    
    return True

def launch_application():
    """启动应用程序"""
    try:
        print("\n正在启动行星轮系参数化建模系统...")
        
        # 导入并运行主应用程序
        from planetary_gear_gui_pyqt import main
        main()
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        print("请确保所有必需的模块都已正确安装")
        return False
    except Exception as e:
        print(f"✗ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("行星轮系参数化建模系统启动器 - PyQt版本")
    print("兼容Python 3.8.10环境")
    print("=" * 60)
    
    # 检查Python版本
    print("\n1. 检查Python版本...")
    if not check_python_version():
        input("按回车键退出...")
        return
    
    # 检查必需包
    print("\n2. 检查必需包...")
    if not check_required_packages():
        input("按回车键退出...")
        return
    
    # 检查项目结构
    print("\n3. 检查项目结构...")
    if not check_project_structure():
        input("按回车键退出...")
        return
    
    # 检查VBA脚本
    print("\n4. 检查VBA脚本...")
    if not check_vba_scripts():
        print("⚠ VBA脚本检查未完全通过，但系统仍可运行")
    
    # 设置环境
    print("\n5. 设置环境...")
    if not setup_environment():
        input("按回车键退出...")
        return
    
    print("\n✓ 所有检查完成，系统准备就绪")
    print("\n启动应用程序...")
    
    # 启动应用程序
    if not launch_application():
        input("按回车键退出...")
        return
    
    print("\n应用程序已退出")

if __name__ == "__main__":
    main()
