import win32com.client
import os
import time
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from tkinter.scrolledtext import ScrolledText
import json
import math
from datetime import datetime

# 尝试导入pycatia，如果不可用则使用win32com
try:
    from pycatia import catia
    PYCATIA_AVAILABLE = True
except ImportError:
    PYCATIA_AVAILABLE = False
    print("⚠ pycatia不可用，将使用win32com进行位置管理")

# 尝试引入自转工具
try:
    from catia_pycatia_position_manager import CATIAPyCatiaPositionManager
    POSITION_MANAGER_AVAILABLE = True
except ImportError as e:
    print(f"⚠ 位置管理器不可用: {e}")
    POSITION_MANAGER_AVAILABLE = False
    # 创建一个虚拟的位置管理器类
    class CATIAPyCatiaPositionManager:
        def __init__(self):
            pass
        def connect(self):
            return False
        def rotate_part_local(self, *args, **kwargs):
            return False

# 导入VBA脚本管理器
from vba_script_manager import VBAScriptManager

class PyCatiaPositionManager:
    """基于pycatia的位置管理器（简化版）"""

    def __init__(self, catia_app=None):
        self.caa = None
        self.document = None
        self.product = None
        self.catia_app = catia_app

    def connect(self):
        """连接到CATIA"""
        try:
            if PYCATIA_AVAILABLE:
                self.caa = catia()
                # 检查是否有活动文档
                try:
                    self.document = self.caa.active_document
                    self.product = self.document.product
                    return True
                except:
                    # 没有活动文档时返回False，但不报错
                    return False
            else:
                # 使用传入的win32com连接
                if self.catia_app:
                    try:
                        self.document = self.catia_app.ActiveDocument
                        self.product = self.document.Product
                        return True
                    except:
                        # 没有活动文档时返回False
                        return False
                return False
        except Exception as e:
            # 只在真正的错误时才打印
            if "active document" not in str(e).lower():
                print(f"位置管理器连接失败: {e}")
            return False

    def get_part_position(self, part_name):
        """获取零件位置"""
        try:
            if not self.product:
                return None

            # 查找零件
            if PYCATIA_AVAILABLE:
                products = self.product.products
                for i in range(products.count):
                    product = products.item(i + 1)
                    if product.name == part_name:
                        position = product.position
                        components = position.get_components()
                        if len(components) >= 12:
                            return {
                                'x': components[9],
                                'y': components[10],
                                'z': components[11],
                                'components': components
                            }
            else:
                # 使用win32com
                products = self.product.Products
                for i in range(1, products.Count + 1):
                    product = products.Item(i)
                    if product.Name == part_name:
                        move = product.Move
                        movable = move.MovableObject
                        # 这里需要获取当前变换矩阵
                        # 由于win32com限制，返回默认值
                        return {
                            'x': 0.0,
                            'y': 0.0,
                            'z': 0.0,
                            'components': [1,0,0,0,1,0,0,0,1,0,0,0]
                        }
            return None
        except Exception as e:
            print(f"获取 {part_name} 位置失败: {e}")
            return None

    def set_part_position_from_components(self, part_name, components):
        """使用组件数组设置零件位置"""
        try:
            if not self.product:
                return False

            if PYCATIA_AVAILABLE:
                products = self.product.products
                for i in range(products.count):
                    product = products.item(i + 1)
                    if product.name == part_name:
                        position = product.position
                        position.set_components(components)
                        return True
            else:
                # 使用win32com
                products = self.product.Products
                for i in range(1, products.Count + 1):
                    product = products.Item(i)
                    if product.Name == part_name:
                        move = product.Move
                        movable = move.MovableObject
                        movable.Apply(components)
                        return True
            return False
        except Exception as e:
            print(f"设置 {part_name} 位置失败: {e}")
            return False

class PlanetaryGearSystemGUI:
    """行星轮系参数化建模和自动装配系统"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("行星轮系参数化建模系统")

        # 设置窗口大小和居中显示
        window_width = 1200
        window_height = 800
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.resizable(True, True)

        # 设置窗口图标和样式
        try:
            self.root.iconbitmap(default="gear.ico")  # 如果有图标文件
        except:
            pass

        # 设置主题样式
        self.style = ttk.Style()
        self.style.theme_use('clam')  # 使用现代主题
        
        # CATIA连接
        self.catia = None
        self.sun_doc = None
        self.planet_doc = None
        self.ring_doc = None
        self.assembly_doc = None

        # 位置管理器
        self.position_manager = PyCatiaPositionManager()

        # VBA脚本管理器（稍后初始化）
        self.vba_manager = None
        
        # 参数变量
        self._init_parameters()
        
        # 创建界面
        self._create_widgets()

        # 延迟计算初始参数
        self.root.after(100, self._calculate_parameters)
    
    def _init_parameters(self):
        """初始化所有参数变量"""
        # 太阳轮参数
        self.sun_z = tk.IntVar(value=38)      # 太阳轮齿数
        self.sun_m = tk.DoubleVar(value=4.2)  # 模数
        self.sun_b = tk.DoubleVar(value=85) # 齿宽
        self.sun_a = tk.DoubleVar(value=20.0) # 压力角
        self.sun_d = tk.DoubleVar(value=10) # 孔径
        
        # 行星轮参数
        self.planet_z = tk.IntVar(value=37)      # 行星轮齿数
        self.planet_m = tk.DoubleVar(value=4.2)  # 模数（应与太阳轮相同）
        self.planet_b = tk.DoubleVar(value=83) # 齿宽
        self.planet_a = tk.DoubleVar(value=20.0) # 压力角
        self.planet_d = tk.DoubleVar(value=8.0)  # 孔径
        self.planet_count = tk.IntVar(value=5)   # 行星轮数量
        
        # 内齿圈参数
        self.ring_z = tk.IntVar(value=112)        # 内齿圈齿数
        self.ring_m = tk.DoubleVar(value=4.2)    # 模数（应与太阳轮相同）
        self.ring_b = tk.DoubleVar(value=70)   # 齿宽
        self.ring_a = tk.DoubleVar(value=20.0)   # 压力角
        self.ring_thickness = tk.DoubleVar(value=15.0)  # 轮缘厚度
        
        # 文件路径
        self.sun_file = tk.StringVar()
        self.planet_file = tk.StringVar()
        self.ring_file = tk.StringVar()
        
        # 计算结果
        self.transmission_ratio = tk.DoubleVar()
        self.center_distance = tk.DoubleVar()
        
    def _create_widgets(self):
        """创建美化的主界面"""
        # 创建标题栏
        self._create_title_bar()

        # 创建主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 使用PanedWindow创建可调整大小的分栏
        paned_window = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned_window.pack(fill=tk.BOTH, expand=True)

        # 左侧面板：参数设置
        left_panel = ttk.Frame(paned_window)
        paned_window.add(left_panel, weight=1)

        # 右侧面板：控制和日志
        right_panel = ttk.Frame(paned_window)
        paned_window.add(right_panel, weight=1)

        # 创建左侧内容
        self._create_parameter_section(left_panel)

        # 创建右侧内容
        self._create_control_section(right_panel)

        # 创建状态栏
        self._create_status_bar()

    def _create_title_bar(self):
        """创建美化的标题栏"""
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill=tk.X, padx=15, pady=(10, 5))

        # 主标题
        title_label = ttk.Label(title_frame, text="行星轮系参数化建模系统",
                               font=('Arial', 16, 'bold'))
        title_label.pack(side=tk.LEFT)

        # 版本信息
        version_label = ttk.Label(title_frame, text="v2.0",
                                 font=('Arial', 10), foreground='gray')
        version_label.pack(side=tk.RIGHT)

        # 分隔线
        separator = ttk.Separator(self.root, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=15, pady=5)

    def _create_status_bar(self):
        """创建状态栏"""
        self.status_frame = ttk.Frame(self.root)
        self.status_frame.pack(fill=tk.X, side=tk.BOTTOM, padx=15, pady=(5, 10))

        # 分隔线
        separator = ttk.Separator(self.root, orient=tk.HORIZONTAL)
        separator.pack(fill=tk.X, padx=15, pady=(0, 5), side=tk.BOTTOM)

        # 状态信息
        self.status_label = ttk.Label(self.status_frame, text="就绪",
                                     font=('Arial', 9))
        self.status_label.pack(side=tk.LEFT)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.status_frame,
                                          variable=self.progress_var,
                                          length=200, mode='determinate')
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))

        # 进度文本
        self.progress_label = ttk.Label(self.status_frame, text="",
                                       font=('Arial', 9))
        self.progress_label.pack(side=tk.RIGHT, padx=(10, 5))

    def _create_parameter_section(self, parent):
        """创建美化的参数设置区域"""
        # 文件选择区
        file_frame = ttk.LabelFrame(parent, text="📁 零件文件选择", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 15))

        # 配置网格权重
        file_frame.columnconfigure(1, weight=1)

        # 太阳轮文件
        ttk.Label(file_frame, text="太阳轮:", font=('Arial', 9, 'bold')).grid(
            row=0, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        sun_entry = ttk.Entry(file_frame, textvariable=self.sun_file, font=('Arial', 9))
        sun_entry.grid(row=0, column=1, sticky=tk.EW, padx=(0, 10), pady=8)
        ttk.Button(file_frame, text="浏览...",
                  command=lambda: self._browse_file(self.sun_file)).grid(
            row=0, column=2, padx=0, pady=8)

        # 行星轮文件
        ttk.Label(file_frame, text="行星轮:", font=('Arial', 9, 'bold')).grid(
            row=1, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        planet_entry = ttk.Entry(file_frame, textvariable=self.planet_file, font=('Arial', 9))
        planet_entry.grid(row=1, column=1, sticky=tk.EW, padx=(0, 10), pady=8)
        ttk.Button(file_frame, text="浏览...",
                  command=lambda: self._browse_file(self.planet_file)).grid(
            row=1, column=2, padx=0, pady=8)

        # 内齿圈文件
        ttk.Label(file_frame, text="内齿圈:", font=('Arial', 9, 'bold')).grid(
            row=2, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        ring_entry = ttk.Entry(file_frame, textvariable=self.ring_file, font=('Arial', 9))
        ring_entry.grid(row=2, column=1, sticky=tk.EW, padx=(0, 10), pady=8)
        ttk.Button(file_frame, text="浏览...",
                  command=lambda: self._browse_file(self.ring_file)).grid(
            row=2, column=2, padx=0, pady=8)

        # 参数设置区
        param_notebook = ttk.Notebook(parent)
        param_notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 太阳轮参数页
        sun_frame = ttk.Frame(param_notebook)
        param_notebook.add(sun_frame, text="☀️ 太阳轮参数")
        self._create_sun_params(sun_frame)

        # 行星轮参数页
        planet_frame = ttk.Frame(param_notebook)
        param_notebook.add(planet_frame, text="🌍 行星轮参数")
        self._create_planet_params(planet_frame)

        # 内齿圈参数页
        ring_frame = ttk.Frame(param_notebook)
        param_notebook.add(ring_frame, text="⭕ 内齿圈参数")
        self._create_ring_params(ring_frame)

        # 计算结果页
        result_frame = ttk.Frame(param_notebook)
        param_notebook.add(result_frame, text="📊 计算结果")
        self._create_result_display(result_frame)
    
    def _create_sun_params(self, parent):
        """创建美化的太阳轮参数设置"""
        frame = ttk.LabelFrame(parent, text="太阳轮参数", padding=15)
        frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 配置网格权重
        frame.columnconfigure(1, weight=1)
        frame.columnconfigure(3, weight=1)

        # 第一行：齿数和模数
        ttk.Label(frame, text="齿数 Z:", font=('Arial', 9, 'bold')).grid(
            row=0, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.sun_z, width=12, font=('Arial', 9)).grid(
            row=0, column=1, sticky=tk.W, padx=(0, 20), pady=8)

        ttk.Label(frame, text="模数 m (mm):", font=('Arial', 9, 'bold')).grid(
            row=0, column=2, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.sun_m, width=12, font=('Arial', 9)).grid(
            row=0, column=3, sticky=tk.W, padx=0, pady=8)

        # 第二行：齿宽和压力角
        ttk.Label(frame, text="齿宽 b (mm):", font=('Arial', 9, 'bold')).grid(
            row=1, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.sun_b, width=12, font=('Arial', 9)).grid(
            row=1, column=1, sticky=tk.W, padx=(0, 20), pady=8)

        ttk.Label(frame, text="压力角 α (°):", font=('Arial', 9, 'bold')).grid(
            row=1, column=2, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.sun_a, width=12, font=('Arial', 9)).grid(
            row=1, column=3, sticky=tk.W, padx=0, pady=8)

        # 第三行：孔径
        ttk.Label(frame, text="孔径 d (mm):", font=('Arial', 9, 'bold')).grid(
            row=2, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.sun_d, width=12, font=('Arial', 9)).grid(
            row=2, column=1, sticky=tk.W, padx=(0, 20), pady=8)
    
    def _create_planet_params(self, parent):
        """创建美化的行星轮参数设置"""
        frame = ttk.LabelFrame(parent, text="行星轮参数", padding=15)
        frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 配置网格权重
        frame.columnconfigure(1, weight=1)
        frame.columnconfigure(3, weight=1)

        # 第一行：齿数和模数
        ttk.Label(frame, text="齿数 Z:", font=('Arial', 9, 'bold')).grid(
            row=0, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.planet_z, width=12, font=('Arial', 9)).grid(
            row=0, column=1, sticky=tk.W, padx=(0, 20), pady=8)

        ttk.Label(frame, text="模数 m (mm):", font=('Arial', 9, 'bold')).grid(
            row=0, column=2, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.planet_m, width=12, font=('Arial', 9)).grid(
            row=0, column=3, sticky=tk.W, padx=0, pady=8)

        # 第二行：齿宽和压力角
        ttk.Label(frame, text="齿宽 b (mm):", font=('Arial', 9, 'bold')).grid(
            row=1, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.planet_b, width=12, font=('Arial', 9)).grid(
            row=1, column=1, sticky=tk.W, padx=(0, 20), pady=8)

        ttk.Label(frame, text="压力角 α (°):", font=('Arial', 9, 'bold')).grid(
            row=1, column=2, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.planet_a, width=12, font=('Arial', 9)).grid(
            row=1, column=3, sticky=tk.W, padx=0, pady=8)

        # 第三行：孔径和行星轮数量
        ttk.Label(frame, text="孔径 d (mm):", font=('Arial', 9, 'bold')).grid(
            row=2, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.planet_d, width=12, font=('Arial', 9)).grid(
            row=2, column=1, sticky=tk.W, padx=(0, 20), pady=8)

        ttk.Label(frame, text="行星轮数量:", font=('Arial', 9, 'bold')).grid(
            row=2, column=2, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Spinbox(frame, from_=2, to=6, textvariable=self.planet_count,
                   width=10, font=('Arial', 9)).grid(
            row=2, column=3, sticky=tk.W, padx=0, pady=8)
    
    def _create_ring_params(self, parent):
        """创建美化的内齿圈参数设置"""
        frame = ttk.LabelFrame(parent, text="内齿圈参数", padding=15)
        frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # 配置网格权重
        frame.columnconfigure(1, weight=1)
        frame.columnconfigure(3, weight=1)

        # 第一行：齿数和模数
        ttk.Label(frame, text="齿数 Z:", font=('Arial', 9, 'bold')).grid(
            row=0, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.ring_z, width=12, font=('Arial', 9)).grid(
            row=0, column=1, sticky=tk.W, padx=(0, 20), pady=8)

        ttk.Label(frame, text="模数 m (mm):", font=('Arial', 9, 'bold')).grid(
            row=0, column=2, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.ring_m, width=12, font=('Arial', 9)).grid(
            row=0, column=3, sticky=tk.W, padx=0, pady=8)

        # 第二行：齿宽和压力角
        ttk.Label(frame, text="齿宽 b (mm):", font=('Arial', 9, 'bold')).grid(
            row=1, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.ring_b, width=12, font=('Arial', 9)).grid(
            row=1, column=1, sticky=tk.W, padx=(0, 20), pady=8)

        ttk.Label(frame, text="压力角 α (°):", font=('Arial', 9, 'bold')).grid(
            row=1, column=2, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.ring_a, width=12, font=('Arial', 9)).grid(
            row=1, column=3, sticky=tk.W, padx=0, pady=8)

        # 第三行：轮缘厚度
        ttk.Label(frame, text="轮缘厚度 (mm):", font=('Arial', 9, 'bold')).grid(
            row=2, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        ttk.Entry(frame, textvariable=self.ring_thickness, width=12, font=('Arial', 9)).grid(
            row=2, column=1, sticky=tk.W, padx=(0, 20), pady=8)
    
    def _create_result_display(self, parent):
        """创建美化的计算结果显示"""
        # 计算结果区
        result_frame = ttk.LabelFrame(parent, text="计算结果", padding=15)
        result_frame.pack(fill=tk.X, padx=15, pady=(15, 10))

        # 配置网格权重
        result_frame.columnconfigure(1, weight=1)
        result_frame.columnconfigure(3, weight=1)

        # 传动比
        ttk.Label(result_frame, text="传动比:", font=('Arial', 9, 'bold')).grid(
            row=0, column=0, sticky=tk.W, padx=(0, 10), pady=8)
        ratio_label = ttk.Label(result_frame, textvariable=self.transmission_ratio,
                               font=('Arial', 9), foreground='blue')
        ratio_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 20), pady=8)

        # 中心距
        ttk.Label(result_frame, text="中心距 (mm):", font=('Arial', 9, 'bold')).grid(
            row=0, column=2, sticky=tk.W, padx=(0, 10), pady=8)
        distance_label = ttk.Label(result_frame, textvariable=self.center_distance,
                                  font=('Arial', 9), foreground='blue')
        distance_label.grid(row=0, column=3, sticky=tk.W, padx=0, pady=8)

        # 计算按钮
        calc_button = ttk.Button(result_frame, text="🔄 重新计算参数",
                                command=self._calculate_parameters)
        calc_button.grid(row=1, column=0, columnspan=4, pady=(15, 10))

        # 参数验证结果区
        validation_frame = ttk.LabelFrame(parent, text="参数验证", padding=15)
        validation_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))

        # 创建文本框和滚动条的容器
        text_container = ttk.Frame(validation_frame)
        text_container.pack(fill=tk.BOTH, expand=True)
        text_container.columnconfigure(0, weight=1)
        text_container.rowconfigure(0, weight=1)

        # 参数验证文本框
        self.validation_text = tk.Text(text_container, height=10, width=50,
                                      font=('Arial', 9), wrap=tk.WORD,
                                      bg='#f8f9fa', relief=tk.FLAT, bd=1)
        self.validation_text.grid(row=0, column=0, sticky=tk.NSEW, padx=(0, 5))

        # 滚动条
        scrollbar = ttk.Scrollbar(text_container, orient=tk.VERTICAL,
                                 command=self.validation_text.yview)
        scrollbar.grid(row=0, column=1, sticky=tk.NS)
        self.validation_text.config(yscrollcommand=scrollbar.set)

    def _create_control_section(self, parent):
        """创建美化的控制和日志区域"""
        # CATIA连接控制
        catia_frame = ttk.LabelFrame(parent, text="🔗 CATIA连接", padding=10)
        catia_frame.pack(fill=tk.X, pady=(0, 15))

        # 连接按钮和状态
        connect_frame = ttk.Frame(catia_frame)
        connect_frame.pack(fill=tk.X)

        ttk.Button(connect_frame, text="🔌 连接CATIA",
                  command=self._connect_catia).pack(side=tk.LEFT, padx=(0, 15))
        self.catia_status = ttk.Label(connect_frame, text="● 未连接",
                                     foreground="red", font=('Arial', 9, 'bold'))
        self.catia_status.pack(side=tk.LEFT)

        # 操作按钮
        operation_frame = ttk.LabelFrame(parent, text="⚙️ 操作控制", padding=10)
        operation_frame.pack(fill=tk.X, pady=(0, 15))

        # 创建按钮网格
        buttons_frame = ttk.Frame(operation_frame)
        buttons_frame.pack(fill=tk.X)
        buttons_frame.columnconfigure(0, weight=1)
        buttons_frame.columnconfigure(1, weight=1)

        # 第一行按钮
        ttk.Button(buttons_frame, text="📂 打开零件文件",
                  command=self._open_parts).grid(row=0, column=0, sticky=tk.EW,
                                               padx=(0, 5), pady=5)
        ttk.Button(buttons_frame, text="🔄 同步参数到CATIA",
                  command=self._sync_parameters).grid(row=0, column=1, sticky=tk.EW,
                                                    padx=(5, 0), pady=5)

        # 第二行按钮
        ttk.Button(buttons_frame, text="📝 生成装配脚本",
                  command=self._generate_assembly_script).grid(row=1, column=0, sticky=tk.EW,
                                                             padx=(0, 5), pady=5)
        ttk.Button(buttons_frame, text="🚀 执行自动装配",
                  command=self._execute_assembly).grid(row=1, column=1, sticky=tk.EW,
                                                     padx=(5, 0), pady=5)

        # 配置管理
        config_frame = ttk.LabelFrame(parent, text="💾 配置管理", padding=10)
        config_frame.pack(fill=tk.X, pady=(0, 15))

        config_buttons_frame = ttk.Frame(config_frame)
        config_buttons_frame.pack(fill=tk.X)

        ttk.Button(config_buttons_frame, text="💾 保存配置",
                  command=self._save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(config_buttons_frame, text="📁 加载配置",
                  command=self._load_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(config_buttons_frame, text="📊 导出报告",
                  command=self._export_report).pack(side=tk.LEFT)

        # 日志区
        log_frame = ttk.LabelFrame(parent, text="📋 操作日志", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 日志工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.pack(fill=tk.X, pady=(0, 5))

        ttk.Button(log_toolbar, text="🗑️ 清空日志",
                  command=self._clear_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(log_toolbar, text="💾 保存日志",
                  command=self._save_log).pack(side=tk.LEFT)

        # 日志文本区域
        self.log_text = ScrolledText(log_frame, height=12, width=45,
                                    font=('Consolas', 9), wrap=tk.WORD,
                                    bg='#f8f9fa', relief=tk.FLAT, bd=1)
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def _log(self, message):
        """添加美化的日志信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 根据消息类型设置颜色
        if "✓" in message or "成功" in message:
            color = "green"
        elif "×" in message or "失败" in message or "错误" in message:
            color = "red"
        elif "⚠" in message or "警告" in message:
            color = "orange"
        elif "步骤" in message:
            color = "blue"
        else:
            color = "black"

        # 插入带颜色的日志
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")

        # 设置最后一行的颜色
        line_start = self.log_text.index("end-2c linestart")
        line_end = self.log_text.index("end-2c lineend")
        self.log_text.tag_add(f"color_{color}", line_start, line_end)
        self.log_text.tag_config(f"color_{color}", foreground=color)

        self.log_text.see(tk.END)
        self.root.update()

        # 更新状态栏
        self._update_status(message)

    def _clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self._log("日志已清空")

    def _save_log(self):
        """保存日志到文件"""
        try:
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="保存日志文件",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                self._log(f"✓ 日志已保存到: {filename}")
        except Exception as e:
            self._log(f"× 保存日志失败: {e}")

    def _update_status(self, message):
        """更新状态栏"""
        if hasattr(self, 'status_label'):
            # 提取状态信息
            if "步骤" in message:
                status = message.split(":")[0] if ":" in message else message
                self.status_label.config(text=status)
            elif "✓" in message:
                self.status_label.config(text="操作完成")
            elif "×" in message:
                self.status_label.config(text="操作失败")
            else:
                self.status_label.config(text="就绪")

    def _browse_file(self, file_var):
        """浏览文件"""
        filetypes = [("CATIA Part", "*.CATPart"), ("All Files", "*.*")]
        filename = filedialog.askopenfilename(title="选择CATPart文件", filetypes=filetypes)
        if filename:
            file_var.set(filename)
            self._log(f"已选择文件: {os.path.basename(filename)}")

    def _connect_catia(self):
        """连接CATIA"""
        self._log("🔌 尝试连接CATIA...")
        try:
            # 优先获取已运行实例
            try:
                self.catia = win32com.client.GetActiveObject("catia.application")
                self._log("✓ 已连接到正在运行的CATIA实例")
            except Exception:
                self.catia = win32com.client.Dispatch("catia.application")
                self._log("✓ 已启动新的CATIA实例")

            self.catia.Visible = True
            self.catia_status.config(text="● 已连接", foreground="green")

            # 设置位置管理器的CATIA引用（延迟连接到有活动文档时）
            self.position_manager.catia_app = self.catia

            # 初始化VBA脚本管理器
            self.vba_manager = VBAScriptManager(self.catia, self._log)
            self._log("✓ CATIA连接成功，位置管理器和VBA脚本管理器已准备就绪")

        except Exception as e:
            self._log(f"× 连接失败: {e}")
            messagebox.showerror("连接失败", f"无法连接到CATIA:\n{e}")
            self.catia = None
            self.catia_status.config(text="● 连接失败", foreground="red")

    def _open_parts(self):
        """打开所有零件文件"""
        if not self.catia:
            messagebox.showwarning("提示", "请先连接CATIA")
            return

        try:
            docs = self.catia.Documents

            # 打开太阳轮
            if self.sun_file.get() and os.path.exists(self.sun_file.get()):
                self.sun_doc = self._safe_open_file(docs, self.sun_file.get(), "太阳轮")

            # 打开行星轮
            if self.planet_file.get() and os.path.exists(self.planet_file.get()):
                self.planet_doc = self._safe_open_file(docs, self.planet_file.get(), "行星轮")

            # 打开内齿圈
            if self.ring_file.get() and os.path.exists(self.ring_file.get()):
                self.ring_doc = self._safe_open_file(docs, self.ring_file.get(), "内齿圈")

        except Exception as e:
            self._log(f"× 打开文件失败: {e}")
            messagebox.showerror("错误", f"打开文件失败:\n{e}")

    def _safe_open_file(self, docs, file_path, file_type):
        """安全地打开CATIA文件，包含重试和错误处理"""
        import time

        full_path = os.path.abspath(file_path)
        self._log(f"正在打开{file_type}: {os.path.basename(file_path)}")

        # 检查文件是否已经打开
        for i in range(1, docs.Count + 1):
            try:
                doc = docs.Item(i)
                if doc.FullName.lower() == full_path.lower():
                    self._log(f"⚠ {file_type}已经打开，使用现有文档")
                    return doc
            except:
                continue

        # 尝试打开文件（最多重试3次）
        for attempt in range(3):
            try:
                doc = docs.Open(full_path)
                self._log(f"✓ {file_type}文件已打开")
                return doc
            except Exception as e:
                if attempt < 2:  # 不是最后一次尝试
                    self._log(f"⚠ {file_type}打开失败，重试中... (尝试 {attempt + 1}/3)")
                    time.sleep(1)  # 等待1秒后重试
                else:
                    # 最后一次尝试失败，抛出异常
                    error_msg = f"{file_type}打开失败: {e}"
                    if "2147352567" in str(e):
                        error_msg += "\n可能原因：文件被占用、权限不足或CATIA版本不兼容"
                    self._log(f"× {error_msg}")
                    raise Exception(error_msg)

        return None

    def _calculate_parameters(self):
        """计算齿轮参数"""
        try:
            # 计算传动比 (正确公式: i = 1 + Zr/Zs)
            ratio = 1 + self.ring_z.get() / self.sun_z.get()
            self.transmission_ratio.set(ratio)

            # 计算中心距
            center_dist = self.sun_m.get() * (self.sun_z.get() + self.planet_z.get()) / 2
            self.center_distance.set(center_dist)

            # 参数验证
            self._validate_parameters()

            self._log(f"✓ 参数计算完成 - 传动比: {ratio:.2f}, 中心距: {center_dist:.2f}mm")

        except Exception as e:
            self._log(f"× 计算失败: {e}")
            messagebox.showerror("计算错误", f"参数计算失败:\n{e}")

    def _validate_parameters(self):
        """验证齿轮参数"""
        self.validation_text.delete(1.0, tk.END)

        validation_results = []

        # 检查模数一致性
        if not (self.sun_m.get() == self.planet_m.get() == self.ring_m.get()):
            validation_results.append("❌ 模数不一致！所有齿轮模数必须相同")
        else:
            validation_results.append("✓ 模数一致")

        # 检查压力角一致性
        if not (self.sun_a.get() == self.planet_a.get() == self.ring_a.get()):
            validation_results.append("❌ 压力角不一致！所有齿轮压力角必须相同")
        else:
            validation_results.append("✓ 压力角一致")

        # 检查齿数关系
        if (self.sun_z.get() + 2 * self.planet_z.get()) != self.ring_z.get():
            validation_results.append("❌ 齿数关系错误！应满足：Zr = Zs + 2*Zp")
        else:
            validation_results.append("✓ 齿数关系正确")

        # 检查行星轮数量与齿数的兼容性
        planet_angle = 360 / self.planet_count.get()
        if (self.sun_z.get() + self.ring_z.get()) % self.planet_count.get() != 0:
            validation_results.append("❌ 行星轮数量与齿数不兼容！可能产生干涉")
        else:
            validation_results.append("✓ 行星轮数量与齿数兼容")

        # 检查中心距
        center_dist = self.sun_m.get() * (self.sun_z.get() + self.planet_z.get()) / 2
        validation_results.append(f"ℹ 中心距: {center_dist:.2f} mm")

        # 检查传动比 (正确公式: i = 1 + Zr/Zs)
        ratio = 1 + self.ring_z.get() / self.sun_z.get()
        validation_results.append(f"ℹ 传动比: {ratio:.2f}")

        # 显示结果
        for result in validation_results:
            self.validation_text.insert(tk.END, result + "\n")


    def _sync_parameters(self):
        """同步参数到CATIA零件"""
        if not self.catia:
            messagebox.showwarning("提示", "请先连接CATIA")
            return

        try:
            # 同步太阳轮参数
            if self.sun_doc:
                self._sync_sun_parameters()
                self._log("✓ 太阳轮参数已同步")

            # 同步行星轮参数
            if self.planet_doc:
                self._sync_planet_parameters()
                self._log("✓ 行星轮参数已同步")

            # 同步内齿圈参数
            if self.ring_doc:
                self._sync_ring_parameters()
                self._log("✓ 内齿圈参数已同步")

            messagebox.showinfo("完成", "所有参数已成功同步到CATIA")

        except Exception as e:
            self._log(f"× 参数同步失败: {e}")
            messagebox.showerror("错误", f"参数同步失败:\n{e}")

    def _sync_sun_parameters(self):
        """同步太阳轮参数"""
        part = self.sun_doc.Part
        params = part.Parameters

        # 根据直齿轮.py的参数名称设置
        params.Item("Z").Value = self.sun_z.get()
        params.Item("b").Value = self.sun_b.get()
        params.Item("m").Value = self.sun_m.get()
        params.Item("a").Value = self.sun_a.get()
        params.Item("dx").Value = self.sun_d.get()

        part.Update()

    def _sync_planet_parameters(self):
        """同步行星轮参数"""
        part = self.planet_doc.Part
        params = part.Parameters

        # 根据直齿轮.py的参数名称设置
        params.Item("Z").Value = self.planet_z.get()
        params.Item("b").Value = self.planet_b.get()
        params.Item("m").Value = self.planet_m.get()
        params.Item("a").Value = self.planet_a.get()
        params.Item("dx").Value = self.planet_d.get()

        part.Update()

    def _sync_ring_parameters(self):
        """同步内齿圈参数"""
        part = self.ring_doc.Part
        params = part.Parameters
        relations = part.Relations

        # 根据内齿圈.py的参数名称设置
        params.Item("z").Value = self.ring_z.get()
        params.Item("L").Value = self.ring_b.get()
        params.Item("m").Value = self.ring_m.get()
        params.Item("α").Value = self.ring_a.get()

        # 设置轮缘厚度公式
        try:
            formula = relations.Item("公式.5")
            formula.modify(f"DD+{self.ring_thickness.get()}mm")
        except:
            self._log("⚠ 未找到轮缘厚度公式，请检查内齿圈模型")

        part.Update()

    def _generate_assembly_script(self):
        """生成装配脚本"""
        try:
            script_content = self._create_assembly_script()

            # 保存脚本文件
            script_path = "generated_assembly.txt"
            with open(script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)

            self._log(f"✓ 装配脚本已生成: {script_path}")
            messagebox.showinfo("完成", f"装配脚本已生成并保存为: {script_path}")

        except Exception as e:
            self._log(f"× 脚本生成失败: {e}")
            messagebox.showerror("错误", f"脚本生成失败:\n{e}")

    def _create_assembly_script(self):
        """创建装配脚本内容"""
        # 计算参数
        center_distance = self.sun_m.get() * (self.sun_z.get() + self.planet_z.get()) / 2
        planet_count = self.planet_count.get()

        # 行星轮角度间隔将由pycatia计算

        # 转换文件路径为正确格式
        sun_path = self.sun_file.get().replace('\\', '\\\\')
        planet_path = self.planet_file.get().replace('\\', '\\\\')
        ring_path = self.ring_file.get().replace('\\', '\\\\')

        script = f'''Language="VBSCRIPT"

' 行星轮系自动装配脚本
' 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
' 参数配置:
'   太阳轮齿数: {self.sun_z.get()}
'   行星轮齿数: {self.planet_z.get()}
'   内齿圈齿数: {self.ring_z.get()}
'   行星轮数量: {planet_count}
'   中心距: {center_distance:.2f} mm

Sub CATMain()

On Error Resume Next

Set productDocument1 = CATIA.ActiveDocument
Set product1 = productDocument1.Product
Set products1 = product1.Products

' 添加太阳轮
Dim arrayOfVariantOfBSTR1(0)
arrayOfVariantOfBSTR1(0) = "{sun_path}"
Call products1.AddComponentsFromFiles(arrayOfVariantOfBSTR1, "All")

Set constraints1 = product1.Connections("CATIAConstraints")
Set reference1 = product1.CreateReferenceFromName("Product1/sun.1/!Product1/sun.1/")
Set constraint1 = constraints1.AddMonoEltCst(catCstTypeReference, reference1)

' 添加行星轮
Dim arrayOfVariantOfBSTR2(0)
arrayOfVariantOfBSTR2(0) = "{planet_path}"
Call products1.AddComponentsFromFiles(arrayOfVariantOfBSTR2, "All")

'''

        # 生成行星轮复制代码
        for i in range(2, planet_count + 1):
            script += f'''
' 复制行星轮 {i}
Set selection{i} = productDocument1.Selection
selection{i}.Clear
Set product{i} = products1.Item("chilun.1")
selection{i}.Add product{i}
selection{i}.Copy
selection{i}.Clear
selection{i}.Add product1
selection{i}.Paste
'''

        # 添加装配约束


        # 注释：行星轮位置将由pycatia精确设置
        script += '''
' 注意：行星轮2及以后的精确位置将由Python pycatia程序设置
' 这样可以获得更高的精度和更好的控制
'''

        # 添加内齿圈装配


        return script

    def _execute_assembly(self):
        """执行自动装配"""
        if not self.catia:
            messagebox.showwarning("提示", "请先连接CATIA")
            return

        try:
            # 创建新的产品文档
            docs = self.catia.Documents
            product_doc = docs.Add("Product")
            self.assembly_doc = product_doc

            self._log("✓ 已创建装配文档")

            # 更新位置管理器的文档引用
            self.position_manager.catia_app = self.catia
            if self.position_manager.connect():
                self._log("✓ 位置管理器已连接到装配文档")
            else:
                self._log("⚠ 位置管理器连接失败，将使用基本装配功能")

            # 使用纯Python方法添加零件（避免VBScript参数问题）
            product = product_doc.Product
            products = product.Products
            self._add_parts_python(products)

            # 添加基本约束
            self._add_basic_constraints_python(product)

            # 使用混合方法进行行星轮复制和定位
            self._create_and_position_planets(product)

            # 注意：内齿圈装配已在_create_and_position_planets中完成，避免重复调用

            # 更新产品
            product.Update()

            self._log("✓ 装配执行完成")
            messagebox.showinfo("完成", "自动装配已完成！")

        except Exception as e:
            self._log(f"× 装配执行失败: {e}")
            messagebox.showerror("错误", f"装配执行失败:\n{e}")

    def _add_parts_python(self, products):
        """使用纯Python方法添加零件（不包括内齿圈，内齿圈由VBA脚本添加）"""
        files_to_add = [
            (self.sun_file.get(), "太阳轮"),
            (self.planet_file.get(), "行星轮")
            # 注意：内齿圈由VBA脚本双击执行时添加
        ]

        for file_path, name in files_to_add:
            if file_path and os.path.exists(file_path):
                try:
                    full_path = os.path.abspath(file_path)
                    products.AddComponentsFromFiles([full_path], "All")
                    self._log(f"✓ {name}已添加")
                except Exception as e:
                    self._log(f"× {name}添加失败: {e}")
                    raise Exception(f"{name}添加失败: {e}")
            else:
                error_msg = f"{name}文件不存在或路径为空: {file_path}"
                self._log(f"× {error_msg}")
                raise Exception(error_msg)

    def _add_basic_constraints_python(self, product):
        """使用纯Python方法添加基本约束"""
        try:
            # 只添加太阳轮的固定约束，其他约束由pycatia处理
            constraints = product.Connections("CATIAConstraints")

            # 尝试添加太阳轮固定约束
            try:
                reference1 = product.CreateReferenceFromName("Product1/sun.1/!Product1/sun.1/")
                constraint1 = constraints.AddMonoEltCst(1, reference1)  # catCstTypeReference = 1
                self._log("✓ 太阳轮固定约束已添加")
            except Exception as e:
                self._log(f"⚠ 太阳轮约束添加失败，将跳过: {e}")
                # 不抛出异常，继续执行

        except Exception as e:
            self._log(f"⚠ 约束系统初始化失败: {e}")
            # 不抛出异常，继续执行

    def _create_and_position_planets(self, product):
        """创建和装配行星轮（新方案）"""
        try:
            planet_count = self.planet_count.get()
            self._log(f"开始装配 {planet_count} 个行星轮...")
            self._log("新装配流程：1.太阳轮固定 → 2.行星轮复制和自转 → 3.VBA实现第一个行星轮啮合 → 4.pycatia获取位置 → 5.VBScript移动其他行星轮 → 6.公转 → 7.VBA内齿圈装配")

            # 步骤1: 太阳轮已经在前面固定了

            # 步骤2: 先用Python直接定位第一个行星轮到中心距位置
            self._position_first_planet_directly(product)

            # 步骤3: 使用VBA脚本实现第一个行星齿轮的啮合约束
            first_planet_success = self._mesh_first_planet_with_vba(product)

            # 只有第一个行星轮啮合成功后才继续后续步骤
            if not first_planet_success:
                self._log("× 装配流程中断：第一个行星轮啮合失败")
                self._log("× 请检查CATIA中的错误信息，解决问题后重新运行")
                return

            # 步骤4: 复制并自转其余行星轮
            if planet_count > 1:
                self._copy_planets_and_apply_rotation_new(product, planet_count)

            # 步骤5: 进行公转
            if planet_count > 1:
                self._revolve_all_planets(product, planet_count)

            # 步骤6: 使用VBA脚本装配内齿圈
            self._assemble_ring_gear_with_vba(product)

            # 偶偶修正
            if self.planet_z.get() % 2 == 0 and self.ring_z.get() % 2 == 0:
                delta = 180.0 / self.ring_z.get()
                self._log(f"偶-偶修正：删除齿圈啮合约束并旋转 {delta:.3f}°")
                self.vba_manager.delete_ring_constraint()
                self.vba_manager.rotate_ring(delta)

            self._log("✓ 行星轮装配流程完成")

        except Exception as e:
            self._log(f"× 行星轮装配失败: {e}")
            # 不抛出异常，继续执行

    def _copy_planets_and_apply_rotation_new(self, product, planet_count):
        """步骤2: 复制行星轮并完成自转（新方案 - 第2个往后才用自转）"""
        try:
            self._log(f"步骤2: 复制 {planet_count-1} 个行星轮并应用自转...")

            products = product.Products
            selection = self.assembly_doc.Selection
            first_planet = products.Item("chilun.1")

            # 获取齿轮参数
            sun_z = self.sun_z.get()
            planet_z = self.planet_z.get()
            ring_z = self.ring_z.get()

            # 计算传动比 (正确公式: i = 1 + Zr/Zs)
            transmission_ratio = 1 + ring_z / sun_z

            self._log(f"  齿轮参数:")
            self._log(f"    太阳轮齿数 (Zs): {sun_z}")
            self._log(f"    行星轮齿数 (Zp): {planet_z}")
            self._log(f"    内齿圈齿数 (Zr): {ring_z}")
            self._log(f"    传动比 (i = 1 + Zr/Zs): 1 + {ring_z}/{sun_z} = {transmission_ratio:.3f}")

            # 自转角度计算公式说明
            self._log(f"  自转角度计算公式:")
            self._log(f"    θ = (i-1) × 360°/n × (k-1) × Zs/Zp")
            self._log(f"    其中: i=传动比, n=行星轮数量, k=行星轮序号, Zs=太阳轮齿数, Zp=行星轮齿数")
            self._log(f"    注意: 第1个行星轮通过约束定位，不需要自转计算")

            # 复制其他行星轮并应用自转（只有第二个行星轮往后才用自转）
            for i in range(2, planet_count + 1):
                try:
                    self._log(f"  处理第{i}个行星轮...")

                    # 复制操作
                    selection.Clear()
                    selection.Add(first_planet)
                    selection.Copy()
                    selection.Clear()
                    selection.Add(product)
                    selection.Paste()

                    # 详细计算自转角度（只有第二个行星轮往后才用自转）
                    self._log(f"    自转角度计算过程:")
                    self._log(f"      θ{i} = ({transmission_ratio:.3f}-1) × 360°/{planet_count} × ({i}-1) × {sun_z}/{planet_z}")

                    step1 = transmission_ratio - 1
                    self._log(f"      步骤1: (i-1) = {transmission_ratio:.3f} - 1 = {step1:.3f}")

                    step2 = 360.0 / planet_count
                    self._log(f"      步骤2: 360°/n = 360°/{planet_count} = {step2:.2f}°")

                    step3 = i - 1
                    self._log(f"      步骤3: (k-1) = {i} - 1 = {step3}")

                    step4 = sun_z / planet_z
                    self._log(f"      步骤4: Zs/Zp = {sun_z}/{planet_z} = {step4:.3f}")

                    # 最终计算
                    rotation_angle = -(step1 * step2 * step3 * step4)  # 顺时针取负
                    self._log(f"      计算结果: θ{i} = {step1:.3f} × {step2:.2f}° × {step3} × {step4:.3f} = {rotation_angle:.2f}°")

                    # 角度标准化
                    normalized_angle = rotation_angle % 360
                    if normalized_angle != rotation_angle:
                        self._log(f"      角度标准化: {rotation_angle:.2f}° → {normalized_angle:.2f}°")
                        rotation_angle = normalized_angle

                    # 应用自转
                    planet_item = products.Item(f"chilun.{i}")
                    self._apply_planet_rotation(planet_item, rotation_angle)

                    self._log(f"    ✓ 第{i}个行星轮自转完成")

                    # 若是第2个行星轮，自转后弹出确认对话框
                    if i == 2:
                        from tkinter import messagebox
                        cont = messagebox.askyesno("自转确认", f"第{i}个行星轮已自转 {rotation_angle:.2f}°\n是否继续复制/自转后续行星轮？")
                        if not cont:
                            self._log("用户取消后续行星轮复制/自转")
                            break

                except Exception as e:
                    self._log(f"    × 第{i}个行星轮处理失败: {e}")

            # 更新产品
            product.Update()
            self._log(f"  ✓ 已完成 {planet_count-1} 个行星轮的复制和自转")

        except Exception as e:
            self._log(f"  × 行星轮复制和自转失败: {e}")

    def _position_first_planet_directly(self, product):
        """步骤3: 先用Python直接定位第一个行星轮到中心距位置"""
        try:
            self._log("步骤3: 先用Python直接定位第一个行星轮到中心距位置...")

            # 计算中心距
            center_distance = self.sun_m.get() * (self.sun_z.get() + self.planet_z.get()) / 2
            self._log(f"  中心距: {center_distance:.2f}mm")

            # 获取第一个行星轮
            products = product.Products
            first_planet = products.Item("chilun.1")

            # 移动到中心距位置
            move = first_planet.Move
            movable_object = move.MovableObject

            # 创建变换矩阵（移动到x=center_distance的位置）
            transform_matrix = [
                1.0, 0.0, 0.0,
                0.0, 1.0, 0.0,
                0.0, 0.0, 1.0,
                center_distance, 0.0, 0.0
            ]

            movable_object.Apply(transform_matrix)
            product.Update()

            self._log(f"  ✓ 第一个行星轮已移动到位置: x={center_distance:.2f}, y=0, z=0")

        except Exception as e:
            self._log(f"  × 第一个行星轮直接定位失败: {e}")

    def _mesh_first_planet_with_vba(self, product):
        """步骤3.5: 使用VBA脚本实现第一个行星齿轮的啮合"""
        try:
            if not self.vba_manager:
                self._log("  × VBA脚本管理器未初始化")
                return False

            # 计算中心距
            center_distance = self.sun_m.get() * (self.sun_z.get() + self.planet_z.get()) / 2

            self._log("=" * 60)
            self._log("⚠ 关键步骤：第一个行星轮啮合")
            self._log("⚠ 只有此步骤成功完成且无错误提示，才能继续后续步骤")
            self._log("=" * 60)

            # 执行第一个行星轮啮合脚本
            success = self.vba_manager.mesh_first_planet(center_distance, self.planet_z.get())

            if success:
                self._log("✓ 第一个行星轮啮合VBA脚本执行成功，继续后续步骤")
                return True
            else:
                self._log("× 第一个行星轮啮合失败，停止后续步骤")
                self._log("× 请检查CATIA中的错误信息，解决问题后重新运行")
                return False

        except Exception as e:
            self._log(f"  × 第一个行星轮啮合失败: {e}")
            return False


    def _get_first_planet_position_with_pycatia(self):
        """步骤4: 用pycatia获取第一个行星齿轮的位置"""
        try:
            self._log("步骤4: 用pycatia获取第一个行星齿轮的位置...")

            # 尝试导入pycatia
            try:
                from pycatia import catia
                self._log("    ✓ pycatia模块导入成功")
            except ImportError as e:
                self._log(f"    ⚠ pycatia模块导入失败: {e}")
                return self._get_default_first_planet_position()

            # 连接到CATIA
            try:
                caa = catia()
                document = caa.active_document
                product = document.product
                self._log(f"    ✓ pycatia连接成功: {product.name}")
            except Exception as e:
                self._log(f"    ⚠ pycatia连接失败: {e}")
                return self._get_default_first_planet_position()

            # 获取第一个行星轮
            try:
                products = product.products
                first_planet = None

                for i in range(products.count):
                    sub_product = products.item(i + 1)
                    if 'chilun.1' in sub_product.name:
                        first_planet = sub_product
                        break

                if not first_planet:
                    self._log("    ⚠ 未找到第一个行星轮")
                    return self._get_default_first_planet_position()

                self._log(f"    ✓ 找到第一个行星轮: {first_planet.name}")

            except Exception as e:
                self._log(f"    ⚠ 获取第一个行星轮失败: {e}")
                return self._get_default_first_planet_position()

            # 获取位置信息
            try:
                # 尝试多种方法获取位置
                try:
                    # 方法1: 使用position属性
                    position = first_planet.position
                    matrix = []
                    for i in range(12):
                        matrix.append(position.get_matrix_coefficients(i))
                    x, y, z = matrix[9], matrix[10], matrix[11]

                    if x != 0 or y != 0 or z != 0:  # 如果不是原点
                        position_info = {'x': x, 'y': y, 'z': z, 'matrix': matrix}
                        self._log(f"    ✓ 第一个行星轮位置(方法1): x={x:.3f}, y={y:.3f}, z={z:.3f}")
                        return position_info

                except Exception:
                    pass

                try:
                    # 方法2: 使用move属性
                    move = first_planet.move
                    movable = move.movable_object
                    matrix = movable.get_position()
                    x, y, z = matrix[9], matrix[10], matrix[11]

                    position_info = {'x': x, 'y': y, 'z': z, 'matrix': matrix}
                    self._log(f"    ✓ 第一个行星轮位置(方法2): x={x:.3f}, y={y:.3f}, z={z:.3f}")
                    return position_info

                except Exception:
                    pass

                # 如果都失败，使用默认位置
                self._log(f"    ⚠ 无法获取第一个行星轮的实际位置，使用默认位置")
                return self._get_default_first_planet_position()

            except Exception as e:
                self._log(f"    ⚠ 获取位置信息失败: {e}")
                return self._get_default_first_planet_position()

        except Exception as e:
            self._log(f"  × pycatia获取位置失败: {e}")
            return self._get_default_first_planet_position()

    def _move_other_planets_to_first_position(self, product, planet_count, first_planet_position):
        """步骤5: 用VBScript将其他行星轮移动到第一个行星齿轮的位置"""
        try:
            if not self.vba_manager:
                self._log("  × VBA脚本管理器未初始化")
                return False

            self._log("步骤5: 用VBScript将其他行星轮移动到第一个行星齿轮的位置...")

            if not first_planet_position:
                self._log("  ⚠ 第一个行星轮位置信息无效，跳过移动")
                return False

            x = first_planet_position['x']
            y = first_planet_position['y']
            z = first_planet_position['z']

            self._log(f"  目标位置: x={x:.3f}, y={y:.3f}, z={z:.3f}")

            # 移动第2到第n个行星轮
            success_count = 0
            for i in range(2, planet_count + 1):
                success = self.vba_manager.move_planet(i, x, y, z)

                if success:
                    self._log(f"    ✓ 第{i}个行星轮移动成功")
                    success_count += 1
                else:
                    self._log(f"    ⚠ 第{i}个行星轮移动失败")

            self._log("  ✓ 其他行星轮位置同步完成")
            return success_count > 0

        except Exception as e:
            self._log(f"  × 其他行星轮位置同步失败: {e}")
            return False

    def _revolve_all_planets(self, product, planet_count):
        """步骤5: 进行公转（使用VBA脚本）"""
        try:
            if not self.vba_manager:
                self._log("  × VBA脚本管理器未初始化")
                return False

            self._log("步骤5: 行星轮公转...")

            # 计算角度间隔
            angle_step = 360.0 / planet_count
            self._log(f"  公转角度间隔: {angle_step:.2f}°")

            # 为每个行星轮（除第一个外）应用公转
            success_count = 0
            for i in range(2, planet_count + 1):
                # 计算公转角度（顺时针为正，取负）
                revolution_angle = ((i - 1) * angle_step)
                
                # 调用VBA脚本实现公转
                success = self.vba_manager.revolve_planet(i, revolution_angle)

                if success:
                    self._log(f"    ✓ 第{i}个行星轮公转成功: {revolution_angle:.1f}°")
                    success_count += 1
                else:
                    self._log(f"    ⚠ 第{i}个行星轮公转失败")

            self._log("  ✓ 所有行星轮公转完成")
            return success_count > 0

        except Exception as e:
            self._log(f"× 行星轮公转失败: {e}")
            return False

    def _assemble_ring_gear_with_vba(self, product):
        """步骤7: 使用VBA脚本装配内齿圈"""
        try:
            if not self.vba_manager:
                self._log("  × VBA脚本管理器未初始化")
                return False

            self._log("步骤7: 使用VBA脚本装配内齿圈...")

            # 获取内齿圈文件路径
            ring_file_path = self.ring_file.get()
            if not ring_file_path or not os.path.exists(ring_file_path):
                ring_file_path = "C:\\Users\\<USER>\\Desktop\\CATIA齿轮参数化建模\\join\\r.CATPart"

            # 执行内齿圈装配脚本
            success = self.vba_manager.assemble_ring_gear(ring_file_path, self.planet_z.get())

            if success:
                self._log("  ✓ 内齿圈装配VBA脚本执行成功")
                self._log("✓ 步骤7完成：内齿圈装配完成")
            else:
                self._log("  ⚠ 内齿圈装配VBA脚本执行失败")

            return success

        except Exception as e:
            self._log(f"× 步骤7失败：内齿圈装配失败: {e}")
            return False

    def _try_center_plane_constraint(self, product, constraints):
        """尝试创建中心面约束"""
        reference_patterns = [
            # 标准引用模式
            ("Product1/sun.1/!零件几何体/center_plane_s", "Product1/chilun.1/!零件几何体/center_plane_p"),
            # 备选引用模式1
            ("Product1/sun.1/!center_plane_s", "Product1/chilun.1/!center_plane_p"),
            # 备选引用模式2
            ("sun.1/!零件几何体/center_plane_s", "chilun.1/!零件几何体/center_plane_p"),
            # 备选引用模式3
            ("sun.1/!center_plane_s", "chilun.1/!center_plane_p"),
        ]

        for sun_ref, planet_ref in reference_patterns:
            try:
                ref_sun_center = product.CreateReferenceFromName(sun_ref)
                ref_planet_center = product.CreateReferenceFromName(planet_ref)
                constraint_center = constraints.AddBiEltCst(0, ref_sun_center, ref_planet_center)  # catCstTypeOn = 0
                constraint_center.Orientation = 0  # catCstOrientSame = 0
                self._log(f"    ✓ 中心面相合约束已添加 (模式: {sun_ref})")
                return True
            except Exception as e:
                continue

        self._log(f"    ⚠ 中心面约束失败: 所有引用模式都无效")
        return False

    def _try_distance_constraint(self, product, constraints, center_distance):
        """尝试创建中心距约束"""
        reference_patterns = [
            # 标准引用模式
            ("Product1/sun.1/!圆形阵列.3/Z 轴", "Product1/chilun.1/!圆形阵列.3/Z 轴"),
            # 备选引用模式1
            ("Product1/sun.1/!Z 轴", "Product1/chilun.1/!Z 轴"),
            # 备选引用模式2
            ("sun.1/!圆形阵列.3/Z 轴", "chilun.1/!圆形阵列.3/Z 轴"),
            # 备选引用模式3
            ("sun.1/!Z 轴", "chilun.1/!Z 轴"),
        ]

        for sun_ref, planet_ref in reference_patterns:
            try:
                ref_sun_axis = product.CreateReferenceFromName(sun_ref)
                ref_planet_axis = product.CreateReferenceFromName(planet_ref)
                constraint_distance = constraints.AddBiEltCst(2, ref_sun_axis, ref_planet_axis)  # catCstTypeDistance = 2
                length = constraint_distance.Dimension
                length.Value = center_distance
                constraint_distance.DistanceConfig = 0  # catCstDCParallel = 0
                self._log(f"    ✓ 中心距约束已添加: {center_distance:.2f}mm (模式: {sun_ref})")
                return True
            except Exception as e:
                continue

        self._log(f"    ⚠ 中心距约束失败: 所有引用模式都无效")
        return False

    def _try_mesh_constraint(self, product, constraints):
        """尝试创建啮合面约束"""
        # 根据行星轮齿数选择啮合面
        planet_z = self.planet_z.get()

        if planet_z % 2 == 1:  # 奇数齿
            mesh_patterns = [
                ("Product1/sun.1/!零件几何体/chiding_s", "Product1/chilun.1/!零件几何体/chiding_p"),
                ("Product1/sun.1/!chiding_s", "Product1/chilun.1/!chiding_p"),
                ("sun.1/!零件几何体/chiding_s", "chilun.1/!零件几何体/chiding_p"),
                ("sun.1/!chiding_s", "chilun.1/!chiding_p"),
            ]
            mesh_type = "奇数齿啮合面"
        else:  # 偶数齿
            mesh_patterns = [
                ("Product1/sun.1/!零件几何体/chiding_s", "Product1/chilun.1/!零件几何体/chigen_p"),
                ("Product1/sun.1/!chiding_s", "Product1/chilun.1/!chigen_p"),
                ("sun.1/!零件几何体/chiding_s", "chilun.1/!零件几何体/chigen_p"),
                ("sun.1/!chiding_s", "chilun.1/!chigen_p"),
            ]
            mesh_type = "偶数齿啮合面"

        for sun_ref, planet_ref in mesh_patterns:
            try:
                ref_sun_mesh = product.CreateReferenceFromName(sun_ref)
                ref_planet_mesh = product.CreateReferenceFromName(planet_ref)
                constraint_mesh = constraints.AddBiEltCst(0, ref_sun_mesh, ref_planet_mesh)  # catCstTypeOn = 0
                constraint_mesh.Orientation = 0  # catCstOrientSame = 0
                self._log(f"    ✓ {mesh_type}约束已添加 (模式: {sun_ref})")
                return True
            except Exception as e:
                continue

        self._log(f"    ⚠ {mesh_type}约束失败: 所有引用模式都无效")
        return False

    def _try_position_manager_constraints(self, product, center_distance):
        """尝试使用位置管理器定位"""
        success_count = 0

        try:
            self._log("  尝试方法4: 使用位置管理器定位...")

            if hasattr(self, 'position_manager') and self.position_manager:
                # 计算第一个行星轮的位置
                x = center_distance
                y = 0.0
                z = 0.0

                # 使用位置管理器设置位置
                if self.position_manager.set_planet_position(1, x, y, z, 0.0):
                    self._log(f"    ✓ 第一个行星轮位置已设置: ({x:.2f}, {y:.2f}, {z:.2f})")
                    success_count += 1
                else:
                    self._log("    ⚠ 位置管理器设置失败")
            else:
                self._log("    ⚠ 位置管理器不可用")

        except Exception as e:
            self._log(f"  ⚠ 位置管理器方法失败: {e}")

        return success_count


    def _copy_and_constrain_other_planets(self, product, planet_count):
        """复制其他行星轮并添加装配约束"""
        try:
            angle_step = 360.0 / planet_count
            self._log(f"复制其他行星轮，角度间隔: {angle_step:.2f}°")

            # 获取产品和选择对象
            products = product.Products
            selection = self.assembly_doc.Selection
            first_planet = products.Item("chilun.1")

            # 复制其他行星轮
            for i in range(2, planet_count + 1):
                try:
                    self._log(f"处理第{i}个行星轮...")

                    # 复制操作
                    selection.Clear()
                    selection.Add(first_planet)
                    selection.Copy()
                    selection.Clear()
                    selection.Add(product)
                    selection.Paste()

                    # 计算自转角度（根据装配.txt中的公式）
                    transmission_ratio = 1 + self.ring_z.get() / self.sun_z.get()
                    rotation_angle = (transmission_ratio - 1) * 360.0 / planet_count * (i - 1) * self.sun_z.get() / self.planet_z.get()

                    self._log(f"第{i}个行星轮自转角度: {rotation_angle:.2f}°")

                    # 应用自转
                    planet_item = products.Item(f"chilun.{i}")
                    self._apply_planet_rotation(planet_item, rotation_angle)

                    # 应用公转位置（反方向旋转，加负号）
                    angle = -((i - 1) * angle_step)
                    self._apply_planet_revolution(planet_item, angle)

                    self._log(f"✓ 第{i}个行星轮装配完成")

                except Exception as e:
                    self._log(f"× 第{i}个行星轮处理失败: {e}")

            # 更新产品
            product.Update()
            self._log(f"✓ 已完成 {planet_count} 个行星轮的装配")

        except Exception as e:
            self._log(f"× 其他行星轮装配失败: {e}")

    def _apply_planet_rotation(self, planet_item, rotation_angle):
        """应用行星轮自转（调用 CATIAPyCatiaPositionManager）"""
        try:
            # 检查位置管理器是否可用
            if not POSITION_MANAGER_AVAILABLE:
                raise RuntimeError("位置管理器不可用")

            # 使用 PyCatiaPositionManager 进行绕自身轴旋转
            pm = CATIAPyCatiaPositionManager()
            if not pm.connect():
                raise RuntimeError("无法连接CATIA进行自转")
            part_name = planet_item.Name  # chilun.i
            pm.rotate_part_local(part_name, axis=(0,0,1), angle_deg=rotation_angle)
            self._log(f"  ✓ 应用自转角度: {rotation_angle:.2f}° (pycatia)")
        except Exception as e:
            # 回退到原矩阵方式
            try:
                move = planet_item.Move
                movable = move.MovableObject
                angle_rad = math.radians(rotation_angle)
                cos_a = math.cos(angle_rad)
                sin_a = math.sin(angle_rad)
                rot = [cos_a,-sin_a,0.0, sin_a,cos_a,0.0, 0.0,0.0,1.0, 0.0,0.0,0.0]
                movable.Apply(rot)
                self._log(f"  ⚠ 使用备选矩阵方式自转成功: {rotation_angle:.2f}°")
            except Exception as ee:
                self._log(f"  × 自转应用失败: {e}; 备选方式也失败: {ee}")

    def _apply_planet_revolution(self, planet_item, revolution_angle):
        """应用行星轮公转位置"""
        try:
            center_distance = self.sun_m.get() * (self.sun_z.get() + self.planet_z.get()) / 2

            # 计算公转位置
            angle_rad = math.radians(revolution_angle)
            x_pos = center_distance * math.cos(angle_rad)
            y_pos = center_distance * math.sin(angle_rad)

            move = planet_item.Move
            movable = move.MovableObject

            # 应用公转位置
            translation_matrix = [
                1.0, 0.0, 0.0,
                0.0, 1.0, 0.0,
                0.0, 0.0, 1.0,
                x_pos, y_pos, 0.0
            ]

            movable.Apply(translation_matrix)
            self._log(f"  ✓ 应用公转位置: ({x_pos:.2f}, {y_pos:.2f}) - {revolution_angle:.1f}°")

        except Exception as e:
            self._log(f"  ⚠ 公转位置应用失败: {e}")

    def _sync_and_revolve_other_planets(self, product, planet_count):
        """步骤4: 获取第一个行星轮位置，同步其他行星轮位置并公转"""
        try:
            self._log("步骤4: 同步其他行星轮位置并实现公转...")

            # 4.1 使用pycatia获取第一个行星轮的位置
            first_planet_pos = self._get_first_planet_position_with_pycatia()
            if not first_planet_pos:
                self._log("⚠ 无法获取第一个行星轮位置，使用计算值")
                center_distance = self.sun_m.get() * (self.sun_z.get() + self.planet_z.get()) / 2
                first_planet_pos = {'x': center_distance, 'y': 0.0, 'z': 0.0}

            self._log(f"  第一个行星轮位置: ({first_planet_pos['x']:.2f}, {first_planet_pos['y']:.2f}, {first_planet_pos['z']:.2f})")

            # 4.2 将其他行星轮移动到第一个行星轮的位置（只改变x,y,z）
            self._move_planets_to_first_position(product, planet_count, first_planet_pos)

            # 4.3 实现公转
            self._apply_revolution_to_other_planets(product, planet_count, first_planet_pos)

            self._log("✓ 步骤4完成：其他行星轮位置已同步并完成公转")

        except Exception as e:
            self._log(f"× 步骤4失败: {e}")

    def _move_planets_to_first_position(self, product, planet_count, first_planet_pos):
        """将其他行星轮移动到第一个行星轮的位置（只改变x,y,z）"""
        try:
            self._log("  4.2: 将其他行星轮移动到第一个行星轮位置...")

            products = product.Products

            for i in range(2, planet_count + 1):
                try:
                    planet_item = products.Item(f"chilun.{i}")
                    move = planet_item.Move
                    movable = move.MovableObject

                    # 只改变位置，保持旋转不变
                    translation_matrix = [
                        1.0, 0.0, 0.0,
                        0.0, 1.0, 0.0,
                        0.0, 0.0, 1.0,
                        first_planet_pos['x'], first_planet_pos['y'], first_planet_pos['z']
                    ]

                    movable.Apply(translation_matrix)
                    self._log(f"    ✓ 第{i}个行星轮已移动到第一个行星轮位置")

                except Exception as e:
                    self._log(f"    × 第{i}个行星轮位置同步失败: {e}")

        except Exception as e:
            self._log(f"  × 位置同步失败: {e}")

    def _apply_revolution_to_other_planets(self, product, planet_count, center_pos):
        """实现其他行星轮的公转"""
        try:
            self._log("  4.3: 实现其他行星轮公转...")

            products = product.Products
            angle_step = 360.0 / planet_count
            center_distance = self.sun_m.get() * (self.sun_z.get() + self.planet_z.get()) / 2

            for i in range(2, planet_count + 1):
                try:
                    # 计算公转角度和位置（反方向旋转，加负号）
                    revolution_angle = ((i - 1) * angle_step)
                    angle_rad = math.radians(revolution_angle)
                    x_pos = center_distance * math.cos(angle_rad)
                    y_pos = center_distance * math.sin(angle_rad)

                    planet_item = products.Item(f"chilun.{i}")
                    move = planet_item.Move
                    movable = move.MovableObject

                    # 应用公转位置
                    revolution_matrix = [
                        1.0, 0.0, 0.0,
                        0.0, 1.0, 0.0,
                        0.0, 0.0, 1.0,
                        x_pos, y_pos, center_pos['z']
                    ]

                    movable.Apply(revolution_matrix)
                    self._log(f"    ✓ 第{i}个行星轮公转到: ({x_pos:.2f}, {y_pos:.2f}) - {revolution_angle:.1f}°")

                except Exception as e:
                    self._log(f"    × 第{i}个行星轮公转失败: {e}")

        except Exception as e:
            self._log(f"  × 公转实现失败: {e}")

    def _get_planet_position_with_pycatia(self):
        """使用pycatia获取第一个行星轮的位置"""
        try:
            if not self.position_manager.connect():
                return None

            # 获取第一个行星轮的位置矩阵
            position_matrix = self.position_manager.get_component_position("chilun.1")
            if position_matrix:
                self._log("✓ 已获取行星轮位置信息")
                return position_matrix
            else:
                self._log("⚠ 无法获取行星轮位置")
                return None

        except Exception as e:
            self._log(f"⚠ pycatia获取位置失败: {e}")
            return None

    def _get_default_planet_position(self):
        """获取默认的行星轮位置"""
        # 计算默认中心距
        center_distance = self.sun_m.get() * (self.sun_z.get() + self.planet_z.get()) / 2

        # 返回默认位置矩阵（单位矩阵，X方向偏移中心距）
        return [
            1.0, 0.0, 0.0,
            0.0, 1.0, 0.0,
            0.0, 0.0, 1.0,
            center_distance, 0.0, 0.0
        ]

    def _create_planet_copy_script(self, planet_count, angle_step, center_distance):
        """创建行星轮复制脚本"""
        script = f'''Language="VBSCRIPT"

' 行星轮复制和定位脚本
' 行星轮数量: {planet_count}
' 角度间隔: {angle_step:.2f}度
' 中心距: {center_distance:.2f}mm

Sub CATMain()

On Error Resume Next

Set productDocument1 = CATIA.ActiveDocument
Set product1 = productDocument1.Product
Set products1 = product1.Products

' 获取第一个行星轮
Set firstPlanet = products1.Item("chilun.1")

'''

        # 为每个额外的行星轮生成复制和定位代码
        for i in range(2, planet_count + 1):
            angle = (i - 1) * angle_step
            angle_rad = angle * 3.14159 / 180.0
            x_pos = center_distance * math.cos(angle_rad)
            y_pos = center_distance * math.sin(angle_rad)


        script += '''
' 更新产品
product1.Update

End Sub
'''

        return script

    def _position_planets_with_pycatia(self):
        """使用pycatia精确定位行星轮"""
        try:
            planet_count = self.planet_count.get()
            if planet_count <= 1:
                return

            # 检查位置管理器是否可用
            if not self.position_manager.connect():
                self._log("⚠ 位置管理器不可用，跳过pycatia精确定位")
                return

            self._log("开始使用pycatia进行行星轮精确定位...")

            # 获取第一个行星轮的位置
            planet1_pos = self.position_manager.get_part_position("chilun.1")
            if not planet1_pos:
                self._log("⚠ 无法获取行星轮1的位置，可能零件名称不匹配")
                return

            self._log(f"✓ 获取到行星轮1位置: X={planet1_pos['x']:.3f}, Y={planet1_pos['y']:.3f}, Z={planet1_pos['z']:.3f}")

            # 计算中心距和角度
            center_distance = math.sqrt(planet1_pos['x']**2 + planet1_pos['y']**2)
            angle_step = 360 / planet_count

            # 设置其他行星轮位置
            for i in range(2, planet_count + 1):
                angle = (i - 1) * angle_step
                angle_rad = math.radians(angle)

                # 计算新位置
                new_x = center_distance * math.cos(angle_rad)
                new_y = center_distance * math.sin(angle_rad)
                new_z = planet1_pos['z']

                # 复制原始变换矩阵并修改位置
                new_components = list(planet1_pos['components'])
                new_components[9] = new_x   # X位置
                new_components[10] = new_y  # Y位置
                new_components[11] = new_z  # Z位置

                # 应用旋转（绕Z轴旋转）
                cos_angle = math.cos(angle_rad)
                sin_angle = math.sin(angle_rad)

                # 更新旋转矩阵
                new_components[0] = cos_angle   # r11
                new_components[1] = sin_angle   # r12
                new_components[3] = -sin_angle  # r21
                new_components[4] = cos_angle   # r22

                # 设置行星轮位置
                part_name = f"chilun.{i}"
                if self.position_manager.set_part_position_from_components(part_name, new_components):
                    self._log(f"✓ 行星轮{i}位置已设置: 角度={angle:.1f}°, X={new_x:.3f}, Y={new_y:.3f}")
                else:
                    self._log(f"× 行星轮{i}位置设置失败")

            self._log("✓ 所有行星轮位置设置完成")

        except Exception as e:
            self._log(f"× pycatia位置设置失败: {e}")
            # 如果pycatia失败，继续使用原有的VBScript方法

    def _save_config(self):
        """保存配置"""
        config = {
            "sun_params": {
                "z": self.sun_z.get(),
                "m": self.sun_m.get(),
                "b": self.sun_b.get(),
                "a": self.sun_a.get(),
                "d": self.sun_d.get()
            },
            "planet_params": {
                "z": self.planet_z.get(),
                "m": self.planet_m.get(),
                "b": self.planet_b.get(),
                "a": self.planet_a.get(),
                "d": self.planet_d.get(),
                "count": self.planet_count.get()
            },
            "ring_params": {
                "z": self.ring_z.get(),
                "m": self.ring_m.get(),
                "b": self.ring_b.get(),
                "a": self.ring_a.get(),
                "thickness": self.ring_thickness.get()
            },
            "files": {
                "sun": self.sun_file.get(),
                "planet": self.planet_file.get(),
                "ring": self.ring_file.get()
            }
        }

        filename = filedialog.asksaveasfilename(
            title="保存配置文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            self._log(f"✓ 配置已保存: {filename}")

    def _load_config(self):
        """加载配置"""
        filename = filedialog.askopenfilename(
            title="加载配置文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 加载太阳轮参数
                sun = config.get("sun_params", {})
                self.sun_z.set(sun.get("z", 20))
                self.sun_m.set(sun.get("m", 2.0))
                self.sun_b.set(sun.get("b", 20.0))
                self.sun_a.set(sun.get("a", 20.0))
                self.sun_d.set(sun.get("d", 10.0))

                # 加载行星轮参数
                planet = config.get("planet_params", {})
                self.planet_z.set(planet.get("z", 30))
                self.planet_m.set(planet.get("m", 2.0))
                self.planet_b.set(planet.get("b", 20.0))
                self.planet_a.set(planet.get("a", 20.0))
                self.planet_d.set(planet.get("d", 8.0))
                self.planet_count.set(planet.get("count", 3))

                # 加载内齿圈参数
                ring = config.get("ring_params", {})
                self.ring_z.set(ring.get("z", 80))
                self.ring_m.set(ring.get("m", 2.0))
                self.ring_b.set(ring.get("b", 20.0))
                self.ring_a.set(ring.get("a", 20.0))
                self.ring_thickness.set(ring.get("thickness", 15.0))

                # 加载文件路径
                files = config.get("files", {})
                self.sun_file.set(files.get("sun", ""))
                self.planet_file.set(files.get("planet", ""))
                self.ring_file.set(files.get("ring", ""))

                self._log(f"✓ 配置已加载: {filename}")

            except Exception as e:
                self._log(f"× 配置加载失败: {e}")
                messagebox.showerror("错误", f"配置加载失败:\n{e}")

    def _export_report(self):
        """导出参数报告"""
        try:
            # 计算参数
            self._calculate_parameters()

            report = f"""行星轮系参数化建模报告
生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

=== 太阳轮参数 ===
齿数: {self.sun_z.get()}
模数: {self.sun_m.get()} mm
齿宽: {self.sun_b.get()} mm
压力角: {self.sun_a.get()}°
孔径: {self.sun_d.get()} mm

=== 行星轮参数 ===
齿数: {self.planet_z.get()}
模数: {self.planet_m.get()} mm
齿宽: {self.planet_b.get()} mm
压力角: {self.planet_a.get()}°
孔径: {self.planet_d.get()} mm
数量: {self.planet_count.get()}

=== 内齿圈参数 ===
齿数: {self.ring_z.get()}
模数: {self.ring_m.get()} mm
齿宽: {self.ring_b.get()} mm
压力角: {self.ring_a.get()}°
轮缘厚度: {self.ring_thickness.get()} mm

=== 计算结果 ===
传动比: {self.transmission_ratio.get():.2f}
中心距: {self.center_distance.get():.2f} mm

=== 文件路径 ===
太阳轮: {self.sun_file.get()}
行星轮: {self.planet_file.get()}
内齿圈: {self.ring_file.get()}

=== 参数验证 ===
"""

            # 添加验证结果
            validation_text = self.validation_text.get(1.0, tk.END).strip()
            if validation_text:
                report += validation_text
            else:
                report += "请先执行参数计算以获取验证结果"

            filename = filedialog.asksaveasfilename(
                title="导出参数报告",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report)
                self._log(f"✓ 报告已导出: {filename}")

        except Exception as e:
            self._log(f"× 报告导出失败: {e}")
            messagebox.showerror("错误", f"报告导出失败:\n{e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = PlanetaryGearSystemGUI(root)
    root.mainloop()
