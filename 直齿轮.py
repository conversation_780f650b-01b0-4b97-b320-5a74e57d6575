import win32com.client
import os
import time
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from tkinter.scrolledtext import ScrolledText

class CatiaParamGUI:
    """使用 tkinter 的简单 GUI，用于连接 CATIA、选择零件并修改参数"""

    def __init__(self, root):
        self.root = root
        self.root.title("CATIA参数化建模")
        self.root.geometry("520x380")
        # 固定窗口大小，界面更紧凑
        self.root.resizable(False, False)

        self.catia = None
        self.part_doc = None

        self._create_widgets()

    # --------------------------- GUI 构建 --------------------------- #
    def _create_widgets(self):
        # 文件选择区
        file_frame = ttk.LabelFrame(self.root, text="文件选择")
        file_frame.pack(fill=tk.X, padx=8, pady=4)

        self.file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_var, width=48).grid(row=0, column=0, padx=4, pady=2)
        ttk.Button(file_frame, text="浏览...", command=self._browse_file).grid(row=0, column=1, padx=4, pady=2)

        # 参数区
        param_frame = ttk.LabelFrame(self.root, text="参数设置")
        param_frame.pack(fill=tk.X, padx=8, pady=4)

        self.z_var = tk.IntVar(value=40)
        self.b_var = tk.DoubleVar(value=30.0)
        self.m_var = tk.DoubleVar(value=0.5)
        self.a_var = tk.DoubleVar(value=20.0)
        self.dx_var = tk.DoubleVar(value=10.0)  # 新增中间孔宽度dx

        ttk.Label(param_frame, text="齿数 Z:").grid(row=0, column=0, sticky=tk.W, padx=4, pady=2)
        ttk.Entry(param_frame, textvariable=self.z_var, width=8).grid(row=0, column=1, padx=4, pady=2)

        ttk.Label(param_frame, text="齿宽 b (mm):").grid(row=1, column=0, sticky=tk.W, padx=4, pady=2)
        ttk.Entry(param_frame, textvariable=self.b_var, width=8).grid(row=1, column=1, padx=4, pady=2)

        ttk.Label(param_frame, text="模数 m (mm):").grid(row=2, column=0, sticky=tk.W, padx=4, pady=2)
        ttk.Entry(param_frame, textvariable=self.m_var, width=8).grid(row=2, column=1, padx=4, pady=2)

        ttk.Label(param_frame, text="压力角 α (°):").grid(row=3, column=0, sticky=tk.W, padx=4, pady=2)
        ttk.Entry(param_frame, textvariable=self.a_var, width=8).grid(row=3, column=1, padx=4, pady=2)
        # 新增中间孔宽度dx
        ttk.Label(param_frame, text="孔径 d (mm):").grid(row=4, column=0, sticky=tk.W, padx=4, pady=2)
        ttk.Entry(param_frame, textvariable=self.dx_var, width=8).grid(row=4, column=1, padx=4, pady=2)

        # 按钮区
        btn_frame = ttk.Frame(self.root)
        btn_frame.pack(fill=tk.X, padx=8, pady=4)

        ttk.Button(btn_frame, text="连接 CATIA", command=self._connect_catia).pack(side=tk.LEFT, padx=4, pady=2)
        ttk.Button(btn_frame, text="打开文件", command=self._open_part).pack(side=tk.LEFT, padx=4, pady=2)
        ttk.Button(btn_frame, text="应用参数", command=self._apply_params).pack(side=tk.LEFT, padx=4, pady=2)
        ttk.Button(btn_frame, text="退出", command=self.root.quit).pack(side=tk.RIGHT, padx=4, pady=2)

        # 日志区
        log_frame = ttk.LabelFrame(self.root, text="日志")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=8, pady=4)

        self.log_text = ScrolledText(log_frame, height=6)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # --------------------------- 工具方法 --------------------------- #
    def _log(self, msg: str):
        self.log_text.insert(tk.END, msg + "\n")
        self.log_text.see(tk.END)

    def _browse_file(self):
        filetypes = [("CATIA Part", "*.CATPart"), ("All Files", "*.*")]
        path = filedialog.askopenfilename(title="选择 CATPart 文件", filetypes=filetypes)
        if path:
            self.file_var.set(path)
            self._log(f"已选择文件: {path}")

    # --------------------------- CATIA 相关 --------------------------- #
    def _connect_catia(self):
        self._log("尝试连接 CATIA ...")
        try:
            # 优先获取已运行实例
            try:
                self.catia = win32com.client.GetActiveObject("catia.application")
                self._log("✓ 已连接到正在运行的 CATIA 实例")
            except Exception:
                self.catia = win32com.client.Dispatch("catia.application")
                self._log("✓ 已启动新的 CATIA 实例")
            self.catia.Visible = True
        except Exception as e:
            messagebox.showerror("连接失败", f"无法连接到 CATIA:\n{e}")
            self._log(f"× 连接失败: {e}")
            self.catia = None

    def _open_part(self):
        if not self.catia:
            messagebox.showwarning("提示", "请先连接 CATIA")
            return
        path = self.file_var.get().strip()
        if not path:
            messagebox.showwarning("提示", "请选择 CATPart 文件")
            return
        if not os.path.isfile(path):
            messagebox.showerror("错误", "文件不存在")
            return

        path = os.path.normpath(path)
        self._log(f"打开文件: {path}")
        try:
            docs = self.catia.Documents
            self.part_doc = docs.Open(path)
            self._log("✓ 文件已成功打开")
        except Exception as e:
            messagebox.showerror("错误", f"打开文件失败:\n{e}")
            self._log(f"× 打开失败: {e}")
            self.part_doc = None

    def _apply_params(self):
        if not self.part_doc:
            messagebox.showwarning("提示", "请先打开 CATPart 文件")
            return
        try:
            part = self.part_doc.Part
            params = part.Parameters

            # 设置参数
            params.Item("Z").Value = self.z_var.get()
            params.Item("b").Value = self.b_var.get()
            params.Item("m").Value = self.m_var.get()
            params.Item("a").Value = self.a_var.get()
            params.Item("dx").Value = self.dx_var.get()  # 新增dx参数同步
            
            part.Update()
            self._log("✓ 参数已应用并更新模型")
            messagebox.showinfo("完成", "参数已成功应用")
        except Exception as e:
            self._log(f"× 应用参数失败: {e}")
            messagebox.showerror("错误", f"应用参数失败:\n{e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = CatiaParamGUI(root)
    root.mainloop() 