Language="VBSCRIPT"

#这段代码用于获取当前活动的CATIA产品文档，获取文档中的主产品对象，获取主产品下的子产品集合（用于添加零件）。

Sub CATMain()

Set productDocument1 = CATIA.ActiveDocument

Set product1 = productDocument1.Product

Set products1 = product1.Products

#定义太阳轮零件路径，添加到产品中，并进行固定。

Dim arrayOfVariantOfBSTR1(0)
arrayOfVariantOfBSTR1(0) = "C:\Users\<USER>\Desktop\CATIA齿轮参数化建模\join\sun.CATPart"
products1.AddComponentsFromFiles arrayOfVariantOfBSTR1, "All"

Set constraints1 = product1.Connections("CATIAConstraints")

Set reference1 = product1.CreateReferenceFromName("Product1/sun.1/!Product1/sun.1/")

Set constraint1 = constraints1.AddMonoEltCst(catCstTypeReference, reference1)


#添加行星轮零件并复制实例（根据需要进行行星轮数量的复制）

Dim arrayOfVariantOfBSTR2(0)
arrayOfVariantOfBSTR2(0) = "C:\Users\<USER>\Desktop\CATIA齿轮参数化建模\join\p.CATPart"
products1.AddComponentsFromFiles arrayOfVariantOfBSTR2, "All"

Set productDocument1 = CATIA.ActiveDocument

Set selection1 = productDocument1.Selection

selection1.Clear 

Set product2 = products1.Item("chilun.1")

selection1.Add product2

selection1.Copy 

Set productDocument1 = CATIA.ActiveDocument

Set selection2 = productDocument1.Selection

selection2.Clear 

selection2.Add product1

selection2.Paste 

Set productDocument1 = CATIA.ActiveDocument

Set selection3 = productDocument1.Selection

selection3.Clear 

selection3.Add product1

selection3.Paste 


#将第二、三个行星齿轮自转的角度先设置好 ，具体自转角度公式如下：（传动比-1）x360°/行星轮个数x第n个行星轮x太阳轮齿数/行星轮齿数
#传动比等于1+内齿圈齿数/行星轮齿数

Set product3 = products1.Item("chilun.2")

Set move1 = product3.Move

Set move1 = move1.MovableObject

Dim arrayOfVariantOfDouble1(11)
arrayOfVariantOfDouble1(0) = 0.998398
arrayOfVariantOfDouble1(1) = 0.056574
arrayOfVariantOfDouble1(2) = 0.000000
arrayOfVariantOfDouble1(3) = -0.056574
arrayOfVariantOfDouble1(4) = 0.998398
arrayOfVariantOfDouble1(5) = 0.000000
arrayOfVariantOfDouble1(6) = 0.000000
arrayOfVariantOfDouble1(7) = 0.000000
arrayOfVariantOfDouble1(8) = 1.000000
arrayOfVariantOfDouble1(9) = 0.000000
arrayOfVariantOfDouble1(10) = 0.000000
arrayOfVariantOfDouble1(11) = 0.000000
move1.Apply arrayOfVariantOfDouble1

Set product4 = products1.Item("chilun.3")

Set move2 = product4.Move

Set move2 = move2.MovableObject

Dim arrayOfVariantOfDouble2(11)
arrayOfVariantOfDouble2(0) = 0.993599
arrayOfVariantOfDouble2(1) = 0.112969
arrayOfVariantOfDouble2(2) = 0.000000
arrayOfVariantOfDouble2(3) = -0.112969
arrayOfVariantOfDouble2(4) = 0.993599
arrayOfVariantOfDouble2(5) = 0.000000
arrayOfVariantOfDouble2(6) = 0.000000
arrayOfVariantOfDouble2(7) = 0.000000
arrayOfVariantOfDouble2(8) = 1.000000
arrayOfVariantOfDouble2(9) = 0.000000
arrayOfVariantOfDouble2(10) = 0.000000
arrayOfVariantOfDouble2(11) = 0.000000
move2.Apply arrayOfVariantOfDouble2

Set selection4 = productDocument1.Selection

Set visPropertySet1 = selection4.VisProperties

Set products1 = product3.Parent

Dim bSTR1
bSTR1 = product3.Name

selection4.Add product3

Set products1 = product4.Parent

Dim bSTR2
bSTR2 = product4.Name

selection4.Add product4

Set visPropertySet1 = visPropertySet1.Parent

Dim bSTR3
bSTR3 = visPropertySet1.Name

Dim bSTR4
bSTR4 = visPropertySet1.Name

visPropertySet1.SetShow 1

selection4.Clear 

#将第一个行星轮（不是粘贴复制）与太阳轮进行装配（包括中心面相合、啮合面相合、中心轴线距离设置（本例子中心距150））
#啮合面相合注意如果行星轮的齿数是奇数，那么就让太阳轮的chiding_s和行星轮的chiding_p两个面相合；如果行星轮的齿数是偶数，那么就让太阳轮的chiding_s和行星轮的chigen_p两个面相合
Set move3 = product2.Move

Set move3 = move3.MovableObject

Dim arrayOfVariantOfDouble3(11)
arrayOfVariantOfDouble3(0) = 1.000000
arrayOfVariantOfDouble3(1) = 0.000000
arrayOfVariantOfDouble3(2) = 0.000000
arrayOfVariantOfDouble3(3) = 0.000000
arrayOfVariantOfDouble3(4) = 1.000000
arrayOfVariantOfDouble3(5) = 0.000000
arrayOfVariantOfDouble3(6) = 0.000000
arrayOfVariantOfDouble3(7) = 0.000000
arrayOfVariantOfDouble3(8) = 1.000000
arrayOfVariantOfDouble3(9) = 100.000000
arrayOfVariantOfDouble3(10) = 0.000000
arrayOfVariantOfDouble3(11) = 0.000000
move3.Apply arrayOfVariantOfDouble3

Set constraints1 = product1.Connections("CATIAConstraints")

Set reference2 = product1.CreateReferenceFromName("Product1/sun.1/!零件几何体/center_plane_s")

Set reference3 = product1.CreateReferenceFromName("Product1/chilun.1/!零件几何体/center_plane_p")

Set constraint2 = constraints1.AddBiEltCst(catCstTypeOn, reference2, reference3)

constraint2.Orientation = catCstOrientSame

product1.Update 

Set product1 = product1.ReferenceProduct

Set constraints1 = product1.Connections("CATIAConstraints")

Set reference4 = product1.CreateReferenceFromName("Product1/sun.1/!圆形阵列.3/Z 轴")

Set reference5 = product1.CreateReferenceFromName("Product1/chilun.1/!圆形阵列.3/Z 轴")

Set constraint3 = constraints1.AddBiEltCst(catCstTypeDistance, reference4, reference5)

Set length1 = constraint3.Dimension

length1.Value = 150.000000

constraint3.DistanceConfig = catCstDCParallel

product1.Update 

Set constraints1 = product1.Connections("CATIAConstraints")

Set reference6 = product1.CreateReferenceFromName("Product1/sun.1/!零件几何体/chiding_s")

Set reference7 = product1.CreateReferenceFromName("Product1/chilun.1/!零件几何体/chiding_p")

Set constraint4 = constraints1.AddBiEltCst(catCstTypeOn, reference6, reference7)

constraint4.Orientation = catCstOrientSame

product1.Update 

Set productDocument1 = CATIA.ActiveDocument

Dim bSTR5
bSTR5 = productDocument1.FullName

Set product1 = productDocument1.Product

Dim bSTR6
bSTR6 = productDocument1.Name

Dim bSTR7
bSTR7 = product1.Name

Set products1 = product1.Products

Dim long1
long1 = products1.Count

Set product5 = products1.Item(1)

Dim bSTR8
bSTR8 = product5.Name

Set product2 = products1.Item(2)

Dim bSTR9
bSTR9 = product2.Name

Set position1 = product2.Position

Set CATIA = position1.Application

Set systemService1 = CATIA.SystemService

Dim arrayOfVariantOfObject1(0)
Set arrayOfVariantOfObject1(0) = product2
 ' Cannot print the method call Evaluate for the object SystemService

Set products1 = product1.Products

Dim long2
long2 = products1.Count

Dim long3
long3 = products1.Count

Set product5 = products1.Item(1)

Dim bSTR10
bSTR10 = product5.Name

Set products1 = product1.Products

Dim long4
long4 = products1.Count

Set product5 = products1.Item(1)

Dim bSTR11
bSTR11 = product5.Name

Set position2 = product5.Position

Set CATIA = position2.Application

Set systemService2 = CATIA.SystemService

Dim arrayOfVariantOfObject2(0)
Set arrayOfVariantOfObject2(0) = product5
 ' Cannot print the method call Evaluate for the object SystemService

Dim bSTR12
bSTR12 = product5.Name

Dim bSTR13
bSTR13 = product5.Name

Set product2 = products1.Item(2)

Dim bSTR14
bSTR14 = product2.Name

Set products1 = product1.Products

Dim long5
long5 = products1.Count

Set product5 = products1.Item(1)

Dim bSTR15
bSTR15 = product5.Name

Set product2 = products1.Item(2)

Dim bSTR16
bSTR16 = product2.Name

Set position1 = product2.Position

Set CATIA = position1.Application

Set systemService3 = CATIA.SystemService

Dim arrayOfVariantOfObject3(0)
Set arrayOfVariantOfObject3(0) = product2
 ' Cannot print the method call Evaluate for the object SystemService

Dim bSTR17
bSTR17 = product2.Name

Dim bSTR18
bSTR18 = product2.Name

Set product3 = products1.Item(3)

Dim bSTR19
bSTR19 = product3.Name

Set products1 = product1.Products

Dim long6
long6 = products1.Count

Set product5 = products1.Item(1)

Dim bSTR20
bSTR20 = product5.Name

Set product2 = products1.Item(2)

Dim bSTR21
bSTR21 = product2.Name

Set product3 = products1.Item(3)

Dim bSTR22
bSTR22 = product3.Name

Set position3 = product3.Position

Set CATIA = position3.Application

Set systemService4 = CATIA.SystemService

Dim arrayOfVariantOfObject4(0)
Set arrayOfVariantOfObject4(0) = product3
 ' Cannot print the method call Evaluate for the object SystemService

Dim bSTR23
bSTR23 = product3.Name

Dim bSTR24
bSTR24 = product3.Name

Set product4 = products1.Item(4)

Dim bSTR25
bSTR25 = product4.Name

Set products1 = product1.Products

Dim long7
long7 = products1.Count

Set product5 = products1.Item(1)

Dim bSTR26
bSTR26 = product5.Name

Set product2 = products1.Item(2)

Dim bSTR27
bSTR27 = product2.Name

Set product3 = products1.Item(3)

Dim bSTR28
bSTR28 = product3.Name

Set product4 = products1.Item(4)

Dim bSTR29
bSTR29 = product4.Name

Set position4 = product4.Position

Set CATIA = position4.Application

Set systemService5 = CATIA.SystemService

Dim arrayOfVariantOfObject5(0)
Set arrayOfVariantOfObject5(0) = product4
 ' Cannot print the method call Evaluate for the object SystemService

Dim bSTR30
bSTR30 = product4.Name

Dim bSTR31
bSTR31 = product4.Name

Set selection5 = productDocument1.Selection

Set visPropertySet2 = selection5.VisProperties

Set products1 = product3.Parent

Dim bSTR32
bSTR32 = product3.Name

selection5.Add product3

Set visPropertySet2 = visPropertySet2.Parent

Dim bSTR33
bSTR33 = visPropertySet2.Name

Dim bSTR34
bSTR34 = visPropertySet2.Name

visPropertySet2.SetShow 0

selection5.Clear 


#移动chilun.2到chilun.1所在坐标（通过pycatia的方式获取），接着旋转-120°（三个行星轮每个之间相隔120°）与太阳轮实现装配
Set move1 = product3.Move

Set move1 = move1.MovableObject

Dim arrayOfVariantOfDouble4(11)
arrayOfVariantOfDouble4(0) = 1.000000
arrayOfVariantOfDouble4(1) = 0.000000
arrayOfVariantOfDouble4(2) = 0.000000
arrayOfVariantOfDouble4(3) = 0.000000
arrayOfVariantOfDouble4(4) = 1.000000
arrayOfVariantOfDouble4(5) = 0.000000
arrayOfVariantOfDouble4(6) = 0.000000
arrayOfVariantOfDouble4(7) = 0.000000
arrayOfVariantOfDouble4(8) = 1.000000
arrayOfVariantOfDouble4(9) = 8.378000
arrayOfVariantOfDouble4(10) = 149.766000
arrayOfVariantOfDouble4(11) = 1.000000
move1.Apply arrayOfVariantOfDouble4

Set move1 = product3.Move

Set move1 = move1.MovableObject

Dim arrayOfVariantOfDouble5(11)
arrayOfVariantOfDouble5(0) = -0.500000
arrayOfVariantOfDouble5(1) = 0.866025
arrayOfVariantOfDouble5(2) = 0.000000
arrayOfVariantOfDouble5(3) = -0.866025
arrayOfVariantOfDouble5(4) = -0.500000
arrayOfVariantOfDouble5(5) = 0.000000
arrayOfVariantOfDouble5(6) = 0.000000
arrayOfVariantOfDouble5(7) = 0.000000
arrayOfVariantOfDouble5(8) = 1.000000
arrayOfVariantOfDouble5(9) = 0.000000
arrayOfVariantOfDouble5(10) = 0.000000
arrayOfVariantOfDouble5(11) = 0.000000
move1.Apply arrayOfVariantOfDouble5

Set move1 = product3.Move

Set move1 = move1.MovableObject

Dim arrayOfVariantOfDouble6(11)
arrayOfVariantOfDouble6(0) = -0.500000
arrayOfVariantOfDouble6(1) = -0.866025
arrayOfVariantOfDouble6(2) = 0.000000
arrayOfVariantOfDouble6(3) = 0.866025
arrayOfVariantOfDouble6(4) = -0.500000
arrayOfVariantOfDouble6(5) = 0.000000
arrayOfVariantOfDouble6(6) = 0.000000
arrayOfVariantOfDouble6(7) = 0.000000
arrayOfVariantOfDouble6(8) = 1.000000
arrayOfVariantOfDouble6(9) = 0.000000
arrayOfVariantOfDouble6(10) = 0.000000
arrayOfVariantOfDouble6(11) = 0.000000
move1.Apply arrayOfVariantOfDouble6

Set move1 = product3.Move

Set move1 = move1.MovableObject

Dim arrayOfVariantOfDouble7(11)
arrayOfVariantOfDouble7(0) = -0.500000
arrayOfVariantOfDouble7(1) = -0.866025
arrayOfVariantOfDouble7(2) = 0.000000
arrayOfVariantOfDouble7(3) = 0.866025
arrayOfVariantOfDouble7(4) = -0.500000
arrayOfVariantOfDouble7(5) = 0.000000
arrayOfVariantOfDouble7(6) = 0.000000
arrayOfVariantOfDouble7(7) = 0.000000
arrayOfVariantOfDouble7(8) = 1.000000
arrayOfVariantOfDouble7(9) = 0.000000
arrayOfVariantOfDouble7(10) = 0.000000
arrayOfVariantOfDouble7(11) = 0.000000
move1.Apply arrayOfVariantOfDouble7

Set selection6 = productDocument1.Selection

Set visPropertySet3 = selection6.VisProperties

Set products1 = product4.Parent

Dim bSTR35
bSTR35 = product4.Name

selection6.Add product4

Set visPropertySet3 = visPropertySet3.Parent

Dim bSTR36
bSTR36 = visPropertySet3.Name

Dim bSTR37
bSTR37 = visPropertySet3.Name

visPropertySet3.SetShow 0

selection6.Clear 

#移动chilun.3到chilun.1所在坐标（通过pycatia的方式获取），接着绕着Z轴旋转-240°（三个行星轮每个之间相隔120°）与太阳轮实现装配


Set move2 = product4.Move

Set move2 = move2.MovableObject

Dim arrayOfVariantOfDouble8(11)
arrayOfVariantOfDouble8(0) = 1.000000
arrayOfVariantOfDouble8(1) = 0.000000
arrayOfVariantOfDouble8(2) = 0.000000
arrayOfVariantOfDouble8(3) = -0.000000
arrayOfVariantOfDouble8(4) = 1.000000
arrayOfVariantOfDouble8(5) = 0.000000
arrayOfVariantOfDouble8(6) = 0.000000
arrayOfVariantOfDouble8(7) = 0.000000
arrayOfVariantOfDouble8(8) = 1.000000
arrayOfVariantOfDouble8(9) = 8.378000
arrayOfVariantOfDouble8(10) = 149.766000
arrayOfVariantOfDouble8(11) = 1.000000
move2.Apply arrayOfVariantOfDouble8

Set move2 = product4.Move

Set move2 = move2.MovableObject

Dim arrayOfVariantOfDouble9(11)
arrayOfVariantOfDouble9(0) = -0.500000
arrayOfVariantOfDouble9(1) = 0.866025
arrayOfVariantOfDouble9(2) = 0.000000
arrayOfVariantOfDouble9(3) = -0.866025
arrayOfVariantOfDouble9(4) = -0.500000
arrayOfVariantOfDouble9(5) = 0.000000
arrayOfVariantOfDouble9(6) = 0.000000
arrayOfVariantOfDouble9(7) = 0.000000
arrayOfVariantOfDouble9(8) = 1.000000
arrayOfVariantOfDouble9(9) = 0.000000
arrayOfVariantOfDouble9(10) = 0.000000
arrayOfVariantOfDouble9(11) = 0.000000
move2.Apply arrayOfVariantOfDouble9

End Sub

#在完成太阳轮与相应数量的行星轮啮合后，进行最后一步，齿圈的装配。（包括齿圈中心线与太阳轮中心线重合、齿圈中心面与太阳轮中心面重合、齿圈啮合面与行星轮啮合面重合）
#啮合面相合注意如果行星轮的齿数是奇数，那么就让太阳轮的chiding_s和行星轮的chiding_p两个面相合；如果行星轮的齿数是偶数，那么就让太阳轮的chiding_s和行星轮的chigen_p两个面相合，相应的齿圈也要根据太阳轮选择的面来进行相合
Set 
Language="VBSCRIPT"

Sub CATMain()

Set productDocument1 = CATIA.ActiveDocument

Set product1 = productDocument1.Product

Set products1 = product1.Products

Dim arrayOfVariantOfBSTR1(0)
arrayOfVariantOfBSTR1(0) = "C:\Users\<USER>\Desktop\CATIA齿轮参数化建模\join\r.CATPart"
products1.AddComponentsFromFiles arrayOfVariantOfBSTR1, "All"

Set constraints1 = product1.Connections("CATIAConstraints")

Set reference1 = product1.CreateReferenceFromName("Product1/ring.1/!Axis:(Selection_RSur:(Face:(Brp:(Pad.1;0:(Brp:(Sketch.6;1)));None:();Cf12:());CircPattern.1_ResultOUT;Z0;G8782))")

Set reference2 = product1.CreateReferenceFromName("Product1/sun.1/!Axis:(Selection_RSur:(Face:(Brp:(Pocket.1;0:(Brp:(Sketch.10;1)));None:();Cf12:());Pocket.1_ResultOUT;Z0;G8782))")

Set constraint1 = constraints1.AddBiEltCst(catCstTypeOn, reference1, reference2)

Set constraints1 = product1.Connections("CATIAConstraints")

Set reference3 = product1.CreateReferenceFromName("Product1/ring.1/!渐开线内齿轮/中心面r")

Set reference4 = product1.CreateReferenceFromName("Product1/chilun.1/!零件几何体/center_plane_p")

Set constraint2 = constraints1.AddBiEltCst(catCstTypeOn, reference3, reference4)

constraint2.Orientation = catCstOrientSame

product1.Update 

Set constraints1 = product1.Connections("CATIAConstraints")

Set reference5 = product1.CreateReferenceFromName("Product1/ring.1/!渐开线内齿轮/啮合面r")

Set reference6 = product1.CreateReferenceFromName("Product1/chilun.1/!零件几何体/chiding_p")

Set constraint3 = constraints1.AddBiEltCst(catCstTypeOn, reference5, reference6)

constraint3.Orientation = catCstOrientSame

product1.Update 

End Sub

