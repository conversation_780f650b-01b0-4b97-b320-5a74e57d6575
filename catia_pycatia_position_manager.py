#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CATIA pycatia位置管理器 - 完美解决方案
使用pycatia库实现真实位置的获取和设置
"""

from pycatia import catia
import json
import math
from datetime import datetime

class CATIAPyCatiaPositionManager:
    """基于pycatia的CATIA位置管理器"""
    
    def __init__(self):
        self.caa = None
        self.document = None
        self.product = None
        
    def connect(self):
        """连接到CATIA"""
        try:
            self.caa = catia()
            self.document = self.caa.active_document
            self.product = self.document.product
            print(f"✓ 成功连接到CATIA: {self.document.name}")
            print(f"✓ 根产品: {self.product.name}")
            return True
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def get_part_position(self, part_name):
        """获取零件的真实位置"""
        try:
            # 查找零件
            products = self.product.products
            target_product = None
            
            for i in range(products.count):
                product = products.item(i + 1)
                if product.name == part_name:
                    target_product = product
                    break
            
            if not target_product:
                print(f"❌ 未找到零件: {part_name}")
                return None
            
            # 获取位置
            position = target_product.position
            components = position.get_components()
            
            if len(components) >= 12:
                # 提取位置和旋转信息
                result = {
                    'name': part_name,
                    'x': components[9],
                    'y': components[10],
                    'z': components[11],
                    'rotation_matrix': {
                        'x_axis': [components[0], components[1], components[2]],
                        'y_axis': [components[3], components[4], components[5]],
                        'z_axis': [components[6], components[7], components[8]]
                    },
                    'raw_components': components,
                    'timestamp': datetime.now().isoformat()
                }
                
                # 计算欧拉角
                result['rotation_angles'] = self.calculate_euler_angles(components)
                
                return result
            else:
                print(f"❌ {part_name} 组件数组长度不足")
                return None
                
        except Exception as e:
            print(f"❌ 获取 {part_name} 位置失败: {e}")
            return None
    
    def set_part_position(self, part_name, x, y, z, rx=0, ry=0, rz=0):
        """设置零件位置"""
        try:
            # 查找零件
            products = self.product.products
            target_product = None
            
            for i in range(products.count):
                product = products.item(i + 1)
                if product.name == part_name:
                    target_product = product
                    break
            
            if not target_product:
                print(f"❌ 未找到零件: {part_name}")
                return False
            
            # 计算旋转矩阵
            rx_rad = math.radians(rx)
            ry_rad = math.radians(ry)
            rz_rad = math.radians(rz)
            
            cos_rx, sin_rx = math.cos(rx_rad), math.sin(rx_rad)
            cos_ry, sin_ry = math.cos(ry_rad), math.sin(ry_rad)
            cos_rz, sin_rz = math.cos(rz_rad), math.sin(rz_rad)
            
            # 组合旋转矩阵 (ZYX顺序)
            r11 = cos_ry * cos_rz
            r12 = -cos_ry * sin_rz
            r13 = sin_ry
            r21 = sin_rx * sin_ry * cos_rz + cos_rx * sin_rz
            r22 = -sin_rx * sin_ry * sin_rz + cos_rx * cos_rz
            r23 = -sin_rx * cos_ry
            r31 = -cos_rx * sin_ry * cos_rz + sin_rx * sin_rz
            r32 = cos_rx * sin_ry * sin_rz + sin_rx * cos_rz
            r33 = cos_rx * cos_ry
            
            # 构建组件数组
            components = (
                r11, r12, r13,  # X轴方向向量
                r21, r22, r23,  # Y轴方向向量
                r31, r32, r33,  # Z轴方向向量
                float(x), float(y), float(z)  # 位置坐标
            )
            
            # 设置位置
            position = target_product.position
            position.set_components(components)
            
            print(f"✅ 成功设置 {part_name} 位置: X={x}, Y={y}, Z={z}, RX={rx}°, RY={ry}°, RZ={rz}°")
            return True
            
        except Exception as e:
            print(f"❌ 设置 {part_name} 位置失败: {e}")
            return False
    
    def calculate_euler_angles(self, components):
        """从旋转矩阵计算欧拉角"""
        try:
            r11, r12, r13 = components[0], components[1], components[2]
            r21, r22, r23 = components[3], components[4], components[5]
            r31, r32, r33 = components[6], components[7], components[8]
            
            # 计算欧拉角 (ZYX顺序)
            sy = math.sqrt(r11*r11 + r21*r21)
            
            if sy > 1e-6:
                rx = math.degrees(math.atan2(r32, r33))
                ry = math.degrees(math.atan2(-r31, sy))
                rz = math.degrees(math.atan2(r21, r11))
            else:
                rx = math.degrees(math.atan2(-r23, r22))
                ry = math.degrees(math.atan2(-r31, sy))
                rz = 0.0
            
            return {'rx': rx, 'ry': ry, 'rz': rz}
            
        except Exception as e:
            print(f"计算欧拉角失败: {e}")
            return {'rx': 0.0, 'ry': 0.0, 'rz': 0.0}
    
    def get_all_parts_positions(self):
        """获取所有零件位置"""
        results = {}
        
        try:
            products = self.product.products
            print(f"\n开始获取所有零件位置，共 {products.count} 个零件")
            print("=" * 60)
            
            for i in range(products.count):
                product = products.item(i + 1)
                position_info = self.get_part_position(product.name)
                
                if position_info:
                    results[product.name] = position_info
                    print(f"✅ {product.name}: X={position_info['x']:.3f}, Y={position_info['y']:.3f}, Z={position_info['z']:.3f}")
                    
                    angles = position_info['rotation_angles']
                    print(f"   旋转: RX={angles['rx']:.3f}°, RY={angles['ry']:.3f}°, RZ={angles['rz']:.3f}°")
                else:
                    print(f"❌ {product.name}: 获取位置失败")
            
            print(f"\n✅ 成功获取 {len(results)} 个零件的位置信息")
            return results
            
        except Exception as e:
            print(f"❌ 获取所有位置失败: {e}")
            return {}
    
    def list_all_parts(self):
        """列出所有零件名称"""
        try:
            products = self.product.products
            parts = []
            for i in range(products.count):
                product = products.item(i + 1)
                parts.append(product.name)
            return parts
        except Exception as e:
            print(f"获取零件列表失败: {e}")
            return []
    
    def move_part_relative(self, part_name, dx, dy, dz, drx=0, dry=0, drz=0):
        """相对移动零件"""
        current_pos = self.get_part_position(part_name)
        if current_pos:
            new_x = current_pos['x'] + dx
            new_y = current_pos['y'] + dy
            new_z = current_pos['z'] + dz
            
            current_angles = current_pos['rotation_angles']
            new_rx = current_angles['rx'] + drx
            new_ry = current_angles['ry'] + dry
            new_rz = current_angles['rz'] + drz
            
            return self.set_part_position(part_name, new_x, new_y, new_z, new_rx, new_ry, new_rz)
        else:
            print(f"❌ 无法进行相对移动，获取 {part_name} 当前位置失败")
            return False
    
    def save_positions_to_file(self, filename):
        """保存所有位置到文件"""
        try:
            positions = self.get_all_parts_positions()
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(positions, f, indent=2, ensure_ascii=False)
            print(f"✅ 位置数据已保存到: {filename}")
            return True
        except Exception as e:
            print(f"❌ 保存失败: {e}")
            return False
    
    def load_and_apply_positions(self, filename):
        """从文件加载并应用位置"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                positions = json.load(f)
            
            success_count = 0
            for part_name, pos_data in positions.items():
                angles = pos_data.get('rotation_angles', {'rx': 0, 'ry': 0, 'rz': 0})
                if self.set_part_position(
                    part_name,
                    pos_data['x'], pos_data['y'], pos_data['z'],
                    angles['rx'], angles['ry'], angles['rz']
                ):
                    success_count += 1
            
            print(f"✅ 成功应用 {success_count}/{len(positions)} 个零件的位置")
            return success_count
            
        except Exception as e:
            print(f"❌ 加载并应用位置失败: {e}")
            return 0
    
    def reset_part_to_origin(self, part_name):
        """重置零件到原点"""
        return self.set_part_position(part_name, 0, 0, 0, 0, 0, 0)
    
    def show_position_summary(self):
        """显示位置摘要"""
        positions = self.get_all_parts_positions()
        
        print("\n" + "="*60)
        print("CATIA 零件位置摘要 (pycatia)")
        print("="*60)
        
        for part_name, pos_data in positions.items():
            print(f"\n🔧 {part_name}:")
            print(f"  位置: X={pos_data['x']:.3f}, Y={pos_data['y']:.3f}, Z={pos_data['z']:.3f}")
            
            angles = pos_data['rotation_angles']
            print(f"  旋转: RX={angles['rx']:.3f}°, RY={angles['ry']:.3f}°, RZ={angles['rz']:.3f}°")
            print(f"  更新时间: {pos_data['timestamp']}")

def main():
    """主函数"""
    manager = CATIAPyCatiaPositionManager()
    
    try:
        if not manager.connect():
            return
        
        while True:
            print("\n" + "="*50)
            print("CATIA pycatia位置管理器")
            print("="*50)
            print("1. 获取零件位置")
            print("2. 设置零件位置")
            print("3. 获取所有零件位置")
            print("4. 相对移动零件")
            print("5. 重置零件到原点")
            print("6. 保存位置到文件")
            print("7. 从文件加载并应用位置")
            print("8. 显示位置摘要")
            print("9. 列出所有零件")
            print("0. 退出")
            
            choice = input("\n请选择操作 (0-9): ").strip()
            
            if choice == '1':
                part_name = input("输入零件名称: ").strip()
                pos = manager.get_part_position(part_name)
                if pos:
                    print(f"\n零件 {part_name} 位置:")
                    print(f"  X={pos['x']:.3f}, Y={pos['y']:.3f}, Z={pos['z']:.3f}")
                    angles = pos['rotation_angles']
                    print(f"  RX={angles['rx']:.3f}°, RY={angles['ry']:.3f}°, RZ={angles['rz']:.3f}°")
            
            elif choice == '2':
                part_name = input("输入零件名称: ").strip()
                try:
                    x = float(input("输入X坐标: "))
                    y = float(input("输入Y坐标: "))
                    z = float(input("输入Z坐标: "))
                    rx = float(input("输入X旋转角度 (默认0): ") or "0")
                    ry = float(input("输入Y旋转角度 (默认0): ") or "0")
                    rz = float(input("输入Z旋转角度 (默认0): ") or "0")
                    manager.set_part_position(part_name, x, y, z, rx, ry, rz)
                except ValueError:
                    print("❌ 输入格式错误")
            
            elif choice == '3':
                manager.get_all_parts_positions()
            
            elif choice == '4':
                part_name = input("输入零件名称: ").strip()
                try:
                    dx = float(input("输入X方向移动距离: "))
                    dy = float(input("输入Y方向移动距离: "))
                    dz = float(input("输入Z方向移动距离: "))
                    drx = float(input("输入X旋转增量 (默认0): ") or "0")
                    dry = float(input("输入Y旋转增量 (默认0): ") or "0")
                    drz = float(input("输入Z旋转增量 (默认0): ") or "0")
                    manager.move_part_relative(part_name, dx, dy, dz, drx, dry, drz)
                except ValueError:
                    print("❌ 输入格式错误")
            
            elif choice == '5':
                part_name = input("输入零件名称: ").strip()
                manager.reset_part_to_origin(part_name)
            
            elif choice == '6':
                filename = input("输入保存文件名 (默认: positions.json): ").strip() or "positions.json"
                manager.save_positions_to_file(filename)
            
            elif choice == '7':
                filename = input("输入文件名 (默认: positions.json): ").strip() or "positions.json"
                manager.load_and_apply_positions(filename)
            
            elif choice == '8':
                manager.show_position_summary()
            
            elif choice == '9':
                parts = manager.list_all_parts()
                print(f"\n可用零件: {parts}")
            
            elif choice == '0':
                break
            
            else:
                print("❌ 无效选择")
        
    except Exception as e:
        print(f"执行过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
