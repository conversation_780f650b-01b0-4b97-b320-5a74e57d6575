#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行星轮系参数化建模和自动装配系统 - 启动脚本
作者: AI Assistant
创建时间: 2025-01-29
更新时间: 2025-01-29
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import logging
from pathlib import Path
from datetime import datetime
from config_manager import config_manager

# 配置日志
def setup_logging():
    """设置日志配置"""
    # 从配置管理器获取日志设置
    log_level = config_manager.get("logging.level", "INFO")
    log_to_file = config_manager.get("logging.log_to_file", True)
    log_to_console = config_manager.get("logging.log_to_console", True)
    log_dir = Path(config_manager.get("paths.logs", "logs"))

    # 创建日志目录
    log_dir.mkdir(exist_ok=True)

    # 设置日志级别
    level = getattr(logging, log_level.upper(), logging.INFO)

    # 配置处理器
    handlers = []

    if log_to_file:
        log_file = log_dir / f"system_startup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        handlers.append(file_handler)

    if log_to_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(logging.Formatter('%(levelname)s - %(message)s'))
        handlers.append(console_handler)

    # 配置根日志记录器
    logging.basicConfig(
        level=level,
        handlers=handlers,
        force=True  # 强制重新配置
    )

    return logging.getLogger(__name__)

def check_dependencies():
    """检查依赖项"""
    logger = logging.getLogger(__name__)
    logger.info("开始检查依赖项...")

    # 必需的依赖项
    required_modules = {
        'win32com.client': 'pywin32',
        'tkinter': 'tkinter (通常随Python安装)',
        'json': 'json (Python内置)',
        'math': 'math (Python内置)',
        'pathlib': 'pathlib (Python内置)',
        'datetime': 'datetime (Python内置)'
    }

    # 可选的依赖项
    optional_modules = {
        'pycatia': 'pycatia (可选，用于增强CATIA集成)'
    }

    missing_required = []
    missing_optional = []

    # 检查必需依赖
    for module, package in required_modules.items():
        try:
            __import__(module)
            logger.info(f"✓ {module} - 已安装")
        except ImportError:
            missing_required.append(package)
            logger.error(f"✗ {module} - 未安装")

    # 检查可选依赖
    for module, package in optional_modules.items():
        try:
            __import__(module)
            logger.info(f"✓ {module} - 已安装 (可选)")
        except ImportError:
            missing_optional.append(package)
            logger.warning(f"⚠ {module} - 未安装 (可选)")

    # 处理缺失的必需依赖
    if missing_required:
        error_msg = f"缺少以下必需依赖项:\n{chr(10).join(missing_required)}\n\n请使用以下命令安装:\npip install {' '.join([pkg.split()[0] for pkg in missing_required])}"
        logger.error(f"依赖项检查失败: {missing_required}")
        messagebox.showerror("依赖项错误", error_msg)
        return False

    # 提示可选依赖
    if missing_optional:
        info_msg = f"以下可选依赖项未安装:\n{chr(10).join(missing_optional)}\n\n这些依赖项不是必需的，但可以提供额外功能。"
        logger.info(f"可选依赖项未安装: {missing_optional}")
        messagebox.showinfo("可选依赖项", info_msg)

    logger.info("依赖项检查完成")
    return True

def check_project_structure():
    """检查项目结构和文件"""
    logger = logging.getLogger(__name__)
    logger.info("开始检查项目结构...")

    # 必需的文件和目录
    required_structure = {
        'files': [
            '行星轮系参数化建模系统.py',
            'vba_script_manager.py',
            'config_manager.py'
        ],
        'directories': [
            config_manager.get("paths.vba_scripts", "vba_scripts")
        ]
    }

    # 从配置获取CATIA文件
    catia_files = {}
    catia_config = config_manager.get("catia_files", {})
    for gear_type, file_path in catia_config.items():
        if file_path:
            gear_names = {
                'sun_gear': '太阳轮零件文件',
                'planet_gear': '行星轮零件文件',
                'ring_gear': '内齿圈零件文件'
            }
            catia_files[file_path] = gear_names.get(gear_type, f'{gear_type}零件文件')

    missing_files = []
    missing_dirs = []
    missing_catia = []

    # 检查必需文件
    for file in required_structure['files']:
        if not os.path.exists(file):
            missing_files.append(file)
            logger.error(f"✗ 缺少文件: {file}")
        else:
            logger.info(f"✓ 文件存在: {file}")

    # 检查必需目录
    for directory in required_structure['directories']:
        if not os.path.exists(directory):
            missing_dirs.append(directory)
            logger.error(f"✗ 缺少目录: {directory}")
        else:
            logger.info(f"✓ 目录存在: {directory}")

    # 检查CATIA文件
    for file, description in catia_files.items():
        if not os.path.exists(file):
            missing_catia.append(f"{file} ({description})")
            logger.warning(f"⚠ CATIA文件未找到: {file}")
        else:
            logger.info(f"✓ CATIA文件存在: {file}")

    # 处理缺失的必需文件
    if missing_files or missing_dirs:
        error_items = missing_files + missing_dirs
        error_msg = f"项目结构不完整，缺少以下必需文件/目录:\n{chr(10).join(error_items)}\n\n请确保所有必需文件都在当前目录中。"
        logger.error(f"项目结构检查失败: {error_items}")
        messagebox.showerror("项目结构错误", error_msg)
        return False

    # 提示CATIA文件状态
    if missing_catia:
        warning_msg = f"以下CATIA文件未找到:\n{chr(10).join(missing_catia)}\n\n这些文件不是启动必需的，但在使用相关功能时需要。\n您可以在程序中重新指定文件路径。"
        logger.warning(f"CATIA文件缺失: {missing_catia}")
        messagebox.showwarning("CATIA文件检查", warning_msg)

    logger.info("项目结构检查完成")
    return True

def check_vba_scripts():
    """检查VBA脚本文件"""
    logger = logging.getLogger(__name__)
    logger.info("开始检查VBA脚本...")

    vba_script_dir = Path(config_manager.get("paths.vba_scripts", "vba_scripts"))
    if not vba_script_dir.exists():
        logger.warning("VBA脚本目录不存在")
        return True

    # 预期的VBA脚本文件
    expected_scripts = [
        'assemble_ring_gear.CATScript',
        'mesh_first_planet.CATScript',
        'move_planet.CATScript',
        'revolve_planet.CATScript'
    ]

    missing_scripts = []
    for script in expected_scripts:
        script_path = vba_script_dir / script
        if not script_path.exists():
            missing_scripts.append(script)
            logger.warning(f"⚠ VBA脚本未找到: {script}")
        else:
            logger.info(f"✓ VBA脚本存在: {script}")

    if missing_scripts:
        warning_msg = f"以下VBA脚本文件未找到:\n{chr(10).join(missing_scripts)}\n\n这些脚本用于CATIA自动装配功能。\n如果不使用自动装配功能，可以忽略此警告。"
        logger.warning(f"VBA脚本缺失: {missing_scripts}")
        messagebox.showwarning("VBA脚本检查", warning_msg)

    logger.info("VBA脚本检查完成")
    return True

def setup_environment():
    """设置运行环境"""
    logger = logging.getLogger(__name__)
    logger.info("开始设置运行环境...")

    try:
        # 创建必要的目录
        config_manager.create_directories()

        # 验证配置
        config_errors = config_manager.validate_config()
        if config_errors:
            logger.warning("配置验证发现问题:")
            for error in config_errors:
                logger.warning(f"  - {error}")

            # 显示配置问题警告
            warning_msg = f"配置验证发现以下问题:\n{chr(10).join(config_errors)}\n\n程序仍可运行，但某些功能可能受限。"
            messagebox.showwarning("配置问题", warning_msg)

        logger.info("运行环境设置完成")
        return True

    except Exception as e:
        logger.error(f"设置运行环境失败: {e}")
        return False

def main():
    """主函数"""
    # 设置日志
    logger = setup_logging()

    logger.info("=" * 60)
    logger.info("行星轮系参数化建模和自动装配系统")
    logger.info("=" * 60)
    logger.info(f"Python版本: {sys.version}")
    logger.info(f"工作目录: {os.getcwd()}")

    try:
        # 1. 检查依赖项
        logger.info("步骤 1/5: 检查依赖项")
        if not check_dependencies():
            logger.error("依赖项检查失败，程序退出")
            return

        # 2. 检查项目结构
        logger.info("步骤 2/5: 检查项目结构")
        if not check_project_structure():
            logger.error("项目结构检查失败，程序退出")
            return

        # 3. 检查VBA脚本
        logger.info("步骤 3/5: 检查VBA脚本")
        check_vba_scripts()

        # 4. 设置运行环境
        logger.info("步骤 4/5: 设置运行环境")
        if not setup_environment():
            logger.error("运行环境设置失败，程序退出")
            return

        # 5. 启动主程序
        logger.info("步骤 5/5: 启动主程序")

        # 导入主程序
        from 行星轮系参数化建模系统 import PlanetaryGearSystemGUI
        logger.info("✓ 主程序模块导入成功")

        # 创建主窗口
        root = tk.Tk()

        # 应用UI设置
        window_width = config_manager.get_ui_setting("window_width")
        window_height = config_manager.get_ui_setting("window_height")
        if window_width and window_height:
            # 计算居中位置
            screen_width = root.winfo_screenwidth()
            screen_height = root.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 创建应用实例
        app = PlanetaryGearSystemGUI(root)
        logger.info("✓ GUI界面创建成功")

        # 设置窗口关闭事件
        def on_closing():
            logger.info("用户关闭程序")
            try:
                # 保存配置
                config_manager.save_config()
                logger.info("配置已保存")
            except Exception as e:
                logger.error(f"保存配置失败: {e}")
            finally:
                root.destroy()

        root.protocol("WM_DELETE_WINDOW", on_closing)

        logger.info("✓ 系统启动成功")
        logger.info("✓ GUI界面已打开")

        # 运行主循环
        root.mainloop()
        logger.info("程序正常退出")

    except ImportError as e:
        error_msg = f"无法导入主程序模块:\n{e}\n\n请确保 '行星轮系参数化建模系统.py' 文件在当前目录中。"
        logger.error(f"导入错误: {e}")
        messagebox.showerror("导入错误", error_msg)

    except Exception as e:
        error_msg = f"程序启动失败:\n{e}"
        logger.error(f"启动错误: {e}", exc_info=True)
        messagebox.showerror("启动错误", error_msg)

if __name__ == "__main__":
    main()
