Language="VBSCRIPT"

Sub CATMain()
    ' 移动行星轮到指定位置脚本
    ' 参数将通过文件替换方式传入：{planet_index}, {x}, {y}, {z}
    
    ' 获取活动文档和产品
    Set productDocument1 = CATIA.ActiveDocument
    Set product1 = productDocument1.Product
    Set products1 = product1.Products
    
    ' 获取指定的行星轮
    Set planet = products1.Item("chilun.{planet_index}")
    
    ' 移动到指定位置（只改变x,y,z）
    Set move1 = planet.Move
    Set move1 = move1.MovableObject
    
    Dim arrayOfVariantOfDouble1(11)
    arrayOfVariantOfDouble1(0) = 1.000000
    arrayOfVariantOfDouble1(1) = 0.000000
    arrayOfVariantOfDouble1(2) = 0.000000
    arrayOfVariantOfDouble1(3) = 0.000000
    arrayOfVariantOfDouble1(4) = 1.000000
    arrayOfVariantOfDouble1(5) = 0.000000
    arrayOfVariantOfDouble1(6) = 0.000000
    arrayOfVariantOfDouble1(7) = 0.000000
    arrayOfVariantOfDouble1(8) = 1.000000
    arrayOfVariantOfDouble1(9) = {x}
    arrayOfVariantOfDouble1(10) = {y}
    arrayOfVariantOfDouble1(11) = {z}
    move1.Apply arrayOfVariantOfDouble1
    
    ' 更新产品
    product1.Update
    
End Sub
