Language="VBSCRIPT"

' ==========================================
' 此文件由VBA脚本管理器自动生成
' 生成时间: 2025-07-30 20:53:27
' 模板文件: delete_ring_constraint.CATScript
' ==========================================


Sub CATMain()
    ' 删除包含啮合面r的相合约束
    Dim doc: Set doc = CATIA.ActiveDocument
    Dim prd: Set prd = doc.Product
    Dim cons: Set cons = prd.Connections("CATIAConstraints")
    Dim i
    For i = cons.Count To 1 Step -1
        If InStr(cons.Item(i).Name, "啮合面r") > 0 Then
            cons.Item(i).Delete
        End If
    Next
    prd.Update
End Sub