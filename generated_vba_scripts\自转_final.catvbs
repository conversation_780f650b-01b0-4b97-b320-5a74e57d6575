Language="VBSCRIPT"

Sub CATMain()

Set productDocument1 = CATIA.ActiveDocument

Set product1 = productDocument1.Product

Set products1 = product1.Products

Set product2 = products1.Item("chilun.2")

Set move1 = product2.Move

Set move1 = move1.MovableObject

Dim arrayOfVariantOfDouble1(11)
arrayOfVariantOfDouble1(0) = -0.500000
arrayOfVariantOfDouble1(1) = -0.866025
arrayOfVariantOfDouble1(2) = 0.000000
arrayOfVariantOfDouble1(3) = 0.866025
arrayOfVariantOfDouble1(4) = -0.500000
arrayOfVariantOfDouble1(5) = 0.000000
arrayOfVariantOfDouble1(6) = 0.000000
arrayOfVariantOfDouble1(7) = 0.000000
arrayOfVariantOfDouble1(8) = 1.000000
arrayOfVariantOfDouble1(9) = -165.556344
arrayOfVariantOfDouble1(10) = 15.455089
arrayOfVariantOfDouble1(11) = 0.000000
move1.Apply arrayOfVariantOfDouble1

End Sub
