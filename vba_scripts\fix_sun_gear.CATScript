Language="VBSCRIPT"

Sub CATMain()
    ' 太阳轮固定约束脚本
    ' 将太阳轮固定在装配中
    
    ' 获取活动文档和产品
    Set productDocument1 = CATIA.ActiveDocument
    Set product1 = productDocument1.Product
    Set products1 = product1.Products
    
    ' 获取太阳轮
    Set sun1 = products1.Item("sun.1")
    
    ' 创建约束系统
    Set constraints1 = product1.Connections("CATIAConstraints")
    
    ' 固定太阳轮
    Set reference_sun = product1.CreateReferenceFromName("Product1/sun.1/!Product1/sun.1/")
    Set constraint_fix = constraints1.AddMonoEltCst(0, reference_sun)  ' 0 = 固定约束
    
    ' 更新产品
    product1.Update
    
End Sub
