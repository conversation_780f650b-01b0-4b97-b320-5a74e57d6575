Language="VBSCRIPT"

' ==========================================
' 此文件由VBA脚本管理器自动生成
' 生成时间: 2025-07-30 14:59:07
' 模板文件: move_planet.CATScript
' ==========================================


Sub CATMain()
    ' 移动行星轮到指定位置脚本
    ' 参数将通过文件替换方式传入：3, -8.923428, -95.584373, 1.000000
    
    ' 获取活动文档和产品
    Set productDocument1 = CATIA.ActiveDocument
    Set product1 = productDocument1.Product
    Set products1 = product1.Products
    
    ' 获取指定的行星轮
    Set planet = products1.Item("chilun.3")
    
    ' 移动到指定位置（只改变x,y,z）
    Set move1 = planet.Move
    Set move1 = move1.MovableObject
    
    Dim arrayOfVariantOfDouble1(11)
    arrayOfVariantOfDouble1(0) = 1.000000
    arrayOfVariantOfDouble1(1) = 0.000000
    arrayOfVariantOfDouble1(2) = 0.000000
    arrayOfVariantOfDouble1(3) = 0.000000
    arrayOfVariantOfDouble1(4) = 1.000000
    arrayOfVariantOfDouble1(5) = 0.000000
    arrayOfVariantOfDouble1(6) = 0.000000
    arrayOfVariantOfDouble1(7) = 0.000000
    arrayOfVariantOfDouble1(8) = 1.000000
    arrayOfVariantOfDouble1(9) = -8.923428
    arrayOfVariantOfDouble1(10) = -95.584373
    arrayOfVariantOfDouble1(11) = 1.000000
    move1.Apply arrayOfVariantOfDouble1
    
    ' 更新产品
    product1.Update
    
End Sub
