Language="VBSCRIPT"

' 内齿圈自转脚本，思路与 revolve_planet 一致
' 由 Python 注入 {delta_deg}、{cos_a}、{sin_a}、{neg_sin_a}

Sub CATMain()
    ' 角度参数
    Dim deltaDeg: deltaDeg = {delta_deg}    ' 替换为实际角度

    ' 获取活动文档
    Dim doc: Set doc = CATIA.ActiveDocument
    Dim prd: Set prd = doc.Product
    Dim ring: Set ring = prd.Products.Item("ring.1")  ' 默认内齿圈实例名 r.1

    ' 旋转
    Dim mv: Set mv = ring.Move
    Dim mo: Set mo = mv.MovableObject

    Dim m(11)
    m(0) = {cos_a}
    m(1) = {neg_sin_a}
    m(2) = 0
    m(3) = {sin_a}
    m(4) = {cos_a}
    m(5) = 0
    m(6) = 0
    m(7) = 0
    m(8) = 1
    m(9) = 0
    m(10)= 0
    m(11)= 0

    mo.Apply m
    prd.Update
End Sub