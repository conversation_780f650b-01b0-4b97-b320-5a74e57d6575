#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VBA脚本管理器
负责管理和执行VBA脚本文件
每次装配时生成新的VBA文件，硬编码所有参数值
"""

import os
import tempfile
import shutil
import math
import time
from pathlib import Path


class VBAScriptManager:
    """VBA脚本管理器"""
    
    def __init__(self, catia, log_callback=None):
        """
        初始化VBA脚本管理器
        
        Args:
            catia: CATIA应用程序对象
            log_callback: 日志回调函数
        """
        self.catia = catia
        self.log_callback = log_callback
        self.script_dir = Path("vba_scripts")
        
        # 创建生成的脚本目录
        self.generated_dir = Path("generated_vba_scripts")
        self.generated_dir.mkdir(exist_ok=True)
    
    def _log(self, message):
        """记录日志"""
        if self.log_callback:
            self.log_callback(message)
    
    def _execute_script_file(self, script_path, script_name):
        """执行VBA脚本文件 - 只使用双击方式"""
        try:
            # 确保脚本路径是字符串并转换为绝对路径
            script_path_str = os.path.abspath(str(script_path))

            # 检查文件是否存在
            if not os.path.exists(script_path_str):
                self._log(f"    × {script_name}VBA脚本文件不存在: {script_path_str}")
                return False

            self._log(f"    正在双击执行VBA脚本: {os.path.basename(script_path_str)}")

            # 只使用双击方式执行
            try:
                # 使用os.startfile()打开文件，完全模拟双击行为
                os.startfile(script_path_str)
                self._log(f"    ✓ {script_name}VBA脚本已启动（双击方式）")

                # 等待用户确认脚本执行结果
                self._log(f"    ⚠ 请检查CATIA中的{script_name}执行结果")
                self._log(f"    ⚠ 如果有错误提示，请先解决错误再继续")

                # 询问用户是否执行成功
                import tkinter as tk
                from tkinter import messagebox

                root = tk.Tk()
                root.withdraw()  # 隐藏主窗口

                message = f"{script_name}VBA脚本已启动。\n\n"
                message += "请检查CATIA中的执行结果：\n"
                message += "- 如果执行成功且没有错误提示，请点击\"是\"\n"
                message += "- 如果有错误提示或执行失败，请点击\"否\"\n\n"
                message += "是否执行成功？"

                result = messagebox.askyesno(
                    f"{script_name}执行确认",
                    message
                )

                root.destroy()

                if result:
                    self._log(f"    ✓ 用户确认{script_name}执行成功")
                    return True
                else:
                    self._log(f"    × 用户确认{script_name}执行失败")
                    return False

            except Exception as e:
                self._log(f"    × 双击方式执行失败: {e}")
                return False

        except Exception as e:
            self._log(f"    × {script_name}VBA脚本执行异常: {e}")
            return False
    
    def _generate_script_from_template(self, template_name, output_name, replacements):
        """
        从模板生成新的VBA脚本文件，硬编码所有参数值
        
        Args:
            template_name: 模板文件名
            output_name: 输出文件名
            replacements: 替换字典，key为要替换的文本，value为新值
        
        Returns:
            生成的脚本文件路径
        """
        try:
            # 读取模板文件
            template_path = self.script_dir / template_name
            with open(template_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 执行文本替换（硬编码参数值）
            script_content = template_content
            for old_text, new_text in replacements.items():
                script_content = script_content.replace(old_text, str(new_text))
            
            # 添加生成信息注释
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            generation_comment = f'''
' ==========================================
' 此文件由VBA脚本管理器自动生成
' 生成时间: {timestamp}
' 模板文件: {template_name}
' ==========================================
'''
            
            # 在Language声明后插入生成信息
            lines = script_content.split('\n')
            if lines and lines[0].startswith('Language='):
                lines.insert(1, generation_comment)
                script_content = '\n'.join(lines)
            
            # 生成输出文件路径
            output_path = self.generated_dir / output_name
            
            # 写入新的脚本文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            
            self._log(f"    ✓ 生成VBA脚本: {output_name}")
            return output_path
            
        except Exception as e:
            self._log(f"    × 生成VBA脚本失败: {e}")
            return None

    def mesh_first_planet(self, center_distance, planet_z):
        """执行第一个行星轮啮合脚本"""
        self._log("步骤3.5: 使用VBA脚本实现第一个行星齿轮的啮合...")

        # 确定啮合面类型
        if planet_z % 2 == 0:
            planet_mesh_face = "chigen_p"
            mesh_type = "偶数齿啮合面"
        else:
            planet_mesh_face = "chiding_p"
            mesh_type = "奇数齿啮合面"

        # 准备替换字典（硬编码参数值）
        replacements = {
            '{center_distance}': f"{center_distance:.6f}",
            '{planet_mesh_face}': planet_mesh_face,
            '{mesh_type}': mesh_type
        }

        # 生成新的VBA脚本文件
        timestamp = int(time.time() * 1000)
        output_name = f"mesh_first_planet_{timestamp}.CATScript"
        script_path = self._generate_script_from_template(
            "mesh_first_planet.CATScript",
            output_name,
            replacements
        )

        if script_path:
            try:
                # 执行生成的脚本
                success = self._execute_script_file(script_path, "第一个行星轮啮合")
                return success
            finally:
                # 可选：清理生成的文件（或保留用于调试）
                # try:
                #     os.unlink(script_path)
                # except:
                #     pass
                pass

        return False

    def move_planet(self, planet_index, x, y, z):
        """执行行星轮移动脚本"""
        self._log(f"  移动第{planet_index}个行星轮...")

        # 准备替换字典（硬编码参数值）
        replacements = {
            '{planet_index}': str(planet_index),
            '{x}': f"{x:.6f}",
            '{y}': f"{y:.6f}",
            '{z}': f"{z:.6f}"
        }

        # 生成新的VBA脚本文件
        timestamp = int(time.time() * 1000)
        output_name = f"move_planet_{planet_index}_{timestamp}.CATScript"
        script_path = self._generate_script_from_template(
            "move_planet.CATScript",
            output_name,
            replacements
        )

        if script_path:
            try:
                # 执行生成的脚本
                success = self._execute_script_file(script_path, f"第{planet_index}个行星轮移动")
                return success
            finally:
                # 可选：清理生成的文件
                pass

        return False

    def revolve_planet(self, planet_index, revolution_angle):
        """执行行星轮公转脚本"""
        self._log(f"  第{planet_index}个行星轮公转角度: {revolution_angle:.2f}°")

        # 计算旋转矩阵参数
        angle_rad = math.radians(revolution_angle)
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)
        neg_sin_a = -sin_a

        # 准备替换字典（硬编码参数值）
        replacements = {
            '{planet_index}': str(planet_index),
            '{revolution_angle}': f"{revolution_angle:.2f}",
            '{cos_a}': f"{cos_a:.6f}",
            '{sin_a}': f"{sin_a:.6f}",
            '{neg_sin_a}': f"{neg_sin_a:.6f}"
        }

        # 生成新的VBA脚本文件
        timestamp = int(time.time() * 1000)
        output_name = f"revolve_planet_{planet_index}_{timestamp}.CATScript"
        script_path = self._generate_script_from_template(
            "revolve_planet.CATScript",
            output_name,
            replacements
        )

        if script_path:
            try:
                # 执行生成的脚本
                success = self._execute_script_file(script_path, f"第{planet_index}个行星轮公转")
                return success
            finally:
                # 可选：清理生成的文件
                pass

        return False

    def assemble_ring_gear(self, ring_file_path, planet_z):
        """执行内齿圈装配脚本"""
        self._log("步骤7: 使用VBA脚本装配内齿圈...")

        # 根据行星轮齿数奇偶性确定啮合面类型（用户最新要求）
        if planet_z % 2 == 1:  # 奇数齿
            planet_mesh_face = "chiding_p"  # 行星轮奇数齿时用chiding_p
            ring_mesh_face = "啮合面r"      # 内齿圈固定用啮合面r
            mesh_type = "奇数齿啮合面"
        else:  # 偶数齿
            planet_mesh_face = "chigen_p"   # 行星轮偶数齿时用chigen_p
            ring_mesh_face = "啮合面r"      # 内齿圈固定用啮合面r
            mesh_type = "偶数齿啮合面"

        # 准备替换字典（硬编码参数值）
        # 内齿圈啮合面固定为"啮合面r"，不需要参数替换
        replacements = {
            '{ring_file_path}': ring_file_path.replace('\\', '/'),  # 统一路径分隔符
            '{planet_mesh_face}': planet_mesh_face,
            '{mesh_type}': mesh_type
        }

        # 生成新的VBA脚本文件
        timestamp = int(time.time() * 1000)
        output_name = f"assemble_ring_gear_{timestamp}.CATScript"
        script_path = self._generate_script_from_template(
            "assemble_ring_gear.CATScript",
            output_name,
            replacements
        )

        if script_path:
            try:
                # 执行生成的脚本
                success = self._execute_script_file(script_path, "内齿圈装配")
                return success
            finally:
                # 可选：清理生成的文件
                pass

        return False

    def delete_ring_constraint(self):
        """删除齿圈与行星轮的啮合约束"""
        return self._execute_script_file(
            self._generate_script_from_template("delete_ring_constraint.CATScript",
                                               f"del_ring_{int(time.time()*1000)}.CATScript",{}),
            "删除齿圈啮合约束")

    def rotate_ring(self, delta_deg):
        """旋转齿圈 delta_deg°"""
        cos_a = math.cos(math.radians(delta_deg))
        sin_a = math.sin(math.radians(delta_deg))
        rep = {
            '{delta_deg}': f"{delta_deg:.6f}",
            '{cos_a}': f"{cos_a:.6f}",
            '{sin_a}': f"{sin_a:.6f}",
            '{neg_sin_a}': f"{-sin_a:.6f}"
        }
        path = self._generate_script_from_template("rotate_ring.CATScript",
                                                  f"rotate_ring_{int(time.time()*1000)}.CATScript", rep)
        return self._execute_script_file(path, "齿圈旋转")

    def fix_sun_gear(self):
        """执行太阳轮固定脚本"""
        self._log("步骤2: 使用VBA脚本固定太阳轮...")

        # 生成新的VBA脚本文件
        timestamp = int(time.time() * 1000)
        output_name = f"fix_sun_gear_{timestamp}.CATScript"
        script_path = self._generate_script_from_template(
            "fix_sun_gear.CATScript",
            output_name,
            {}  # 太阳轮固定不需要参数
        )

        if script_path:
            try:
                # 执行生成的脚本
                success = self._execute_script_file(script_path, "太阳轮固定")
                return success
            finally:
                # 可选：清理生成的文件
                pass

        return False
