Language="VBSCRIPT"

' ==========================================
' 此文件由VBA脚本管理器自动生成
' 生成时间: 2025-07-30 21:04:49
' 模板文件: rotate_ring.CATScript
' ==========================================


Sub CATMain()
    ' 2.368421 由 Python 替换
    Dim deltaDeg
    deltaDeg = 2.368421

    Dim doc: Set doc = CATIA.ActiveDocument
    Dim prd: Set prd = doc.Product
    Dim ring: Set ring = prd.Products.Item("r.1")

    Dim mv: Set mv = ring.Move
    Dim mo: Set mo = mv.MovableObject

    Dim rad: rad = deltaDeg * 3.14159265358979 / 180#
    Dim c: c = Cos(rad)
    Dim s: s = Sin(rad)
    Dim tr(11)
    tr(0)=c : tr(1)=-s : tr(2)=0
    tr(3)=s : tr(4)= c : tr(5)=0
    tr(6)=0 : tr(7)= 0 : tr(8)=1
    tr(9)=0 : tr(10)=0: tr(11)=0

    mo.Apply tr
    prd.Update
End Sub